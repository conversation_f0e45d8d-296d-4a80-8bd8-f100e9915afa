# Script để kiểm tra các công cụ phát triển

Write-Host "=== Checking Development Tools ===" -ForegroundColor Green
Write-Host ""

# Danh sách các công cụ cần kiểm tra
$tools = @(
    @{Name="Node.js"; Command="node"; Args="--version"},
    @{Name="npm"; Command="npm"; Args="--version"},
    @{Name="Git"; Command="git"; Args="--version"},
    @{Name="Java"; Command="java"; Args="-version"},
    @{Name="Yarn"; Command="yarn"; Args="--version"},
    @{Name="Python"; Command="python"; Args="--version"},
    @{Name="Docker"; Command="docker"; Args="--version"},
    @{Name="Bun"; Command="bun"; Args="--version"},
    @{Name="Android Debug Bridge (adb)"; Command="adb"; Args="version"}
)

foreach ($tool in $tools) {
    Write-Host -NoNewline "$($tool.Name): " -ForegroundColor Yellow
    
    try {
        $result = & $tool.Command $tool.Args 2>&1 | Out-String
        if ($LASTEXITCODE -eq 0 -or $result) {
            $version = $result.Trim() -split "`n" | Select-Object -First 1
            Write-Host $version -ForegroundColor Green
        } else {
            Write-Host "NOT FOUND" -ForegroundColor Red
        }
    } catch {
        Write-Host "NOT FOUND" -ForegroundColor Red
    }
}

Write-Host "`n=== Current PATH ===" -ForegroundColor Cyan
$env:PATH -split ';' | Where-Object { $_ -ne '' } | ForEach-Object {
    if (Test-Path $_) {
        Write-Host "  [OK] $_" -ForegroundColor Green
    } else {
        Write-Host "  [X] $_" -ForegroundColor Red
    }
} 