import {ScrollView, View} from 'react-native';
import {useTranslation} from 'react-i18next';
import TitleWithBottom from '../Layout/titleWithBottom';
import {ListTree} from '../../modules/Default/tree/default';
import ByRelativeCount from '../../modules/Default/listview/byRelativeCount';
import {ColorThemes} from '../../assets/skin/colors';
import DefaultByParent from '../../modules/Default/listview/byParent';
import FormById from '../../modules/Default/form/formById';

export default function Games() {
  const {t} = useTranslation();
  return (
    <TitleWithBottom title={t('app.name')}>
      {/* <ListTree /> */}
      {/* <DefaultByParent /> */}
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        {/* <ByRelativeCount /> */}
        <FormById formId="0ff16bf176124e3b93a1a9ac35521209" />
      </View>
    </TitleWithBottom>
  );
}
