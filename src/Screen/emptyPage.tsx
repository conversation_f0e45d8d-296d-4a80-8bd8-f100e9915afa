import {ImageBackground, Pressable, Text, View} from 'react-native';
import {ColorThemes} from '../assets/skin/colors';
import {SkeletonImage} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import FastImage from 'react-native-fast-image';

export default function EmptyPage(props: any) {
  const {t} = useTranslation();

  return (
    <Pressable
      style={{
        alignContent: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
      }}>
      {props.hideImg ? null : (
        // <SkeletonImage
        //   source={{
        //     uri: 'https://redis.ktxgroup.com.vn/api/file/img/ce5cc92f4b67415bb2622cf40d0693e8',
        //   }}
        //   style={{height: 200, width: 200}}
        // />
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            width: 200,
            height: 200,
            marginTop: 100,
          }}>
          <FastImage
            source={require('../modules/game/ailatrieuphu/assets/lose-icon.png')}
            style={{
              width: '100%',
              height: '100%',
              position: 'absolute',
            }}
            resizeMode="contain"
          />
          <ImageBackground
            source={require('../modules/game/ailatrieuphu/assets/speech-bubble.png')}
            style={{
              position: 'absolute',
              top: -100,
              right: -80,
              borderRadius: 20,
              padding: 15,
              width: 210,
              height: 159,
            }}>
            <Text
              style={{
                color: '#161C24',
                marginTop: props.hideImg ? 0 : 20,
                fontSize: 16,
                fontWeight: 'bold',
                textAlign: 'center',
              }}>
              {props.title ?? 'Không có gì cả'}
            </Text>
          </ImageBackground>
        </View>
      )}
      {/* {props.title ? (
        <Text
          style={{
            color: '#161C24',
            marginTop: props.hideImg ? 0 : 20,
            fontSize: 16,
            fontWeight: 'bold',
            textAlign: 'center',
          }}>
          {props.title ?? t('common.noData')}
        </Text>
      ) : null} */}
      {props.subtitle ? (
        <Text
          style={{
            color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            marginTop: props.hideImg ? 0 : 20,
            fontSize: 14,
            textAlign: 'center',
          }}>
          {props.subtitle}
        </Text>
      ) : null}
      {props.button ? props.button : null}
    </Pressable>
  );
}
