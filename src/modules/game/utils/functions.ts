function replaceObjectById(newObj: any, array: any) {
  const cloneArray = [...array];
  const index = array.findIndex((item: any) => item.id === newObj.id);
  if (index !== -1) {
    cloneArray[index] = newObj;
  }
  return cloneArray;
}

// Kiểm tra object có sort lớn nhất trong mảng không
function hasMaxSort(arr: any[], obj: any) {
  // Kiểm tra nếu mảng rỗng hoặc object không có thuộc tính sort
  if (!arr || arr.length === 0 || typeof obj.sort !== 'number') {
    return false;
  }

  // Tìm giá trị Sort lớn nhất trong mảng
  const maxSort = Math.max(...arr.map(item => item.sort || 0));

  // So sánh với Sort của object truyền vào
  return obj.sort === maxSort;
}

// Kiểm tra list object đưa vào có đúng thứ tự tăng dần không
const checkPositionOrder = (arr: any[]) => {
  if (arr.length <= 1) {
    return true;
  }

  for (let i = 0; i < arr.length - 1; i++) {
    if (arr[i].sort > arr[i + 1].sort) {
      return false;
    }
  }

  return true;
};

// Xoá object trong array theo type
function removeObjectByType(array: any[], objectToRemove: any, type: string) {
  return array.filter(item => item[type] !== objectToRemove[type]);
}

export {
  replaceObjectById,
  checkPositionOrder,
  removeObjectByType,
  hasMaxSort,
};
