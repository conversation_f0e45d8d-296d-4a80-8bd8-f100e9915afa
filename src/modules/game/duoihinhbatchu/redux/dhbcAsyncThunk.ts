import {createAsyncThunk} from '@reduxjs/toolkit';
import {GetGameConfigRequest} from '../../sakutimban/types/sakuTBTypes';
import {initializeFallbackData} from '../../sakutimban/data/fallbackData';
import {dhbcDa} from '../da/dhbcDA';

const dhbcDA = new dhbcDa();

// Async thunk để load GameConfig từ API
const loadGameConfig = createAsyncThunk(
  'dhbcReducer/loadGameConfig',
  async ({gameId}: GetGameConfigRequest) => {
    try {
      const gameConfig = await dhbcDA.getGameConfig(gameId);
      return gameConfig;
    } catch (error) {
      return {
        configLv1: null,
        configLv2: null,
        configLv3: null,
      };
    }
  },
);

// Async thunk để load questions từ API
const loadGameQuestions = createAsyncThunk(
  'dhbcReducer/loadQuestions',
  async ({
    gameId,
    stage,
    competenceId,
  }: {
    gameId: string;
    stage: number;
    competenceId: string;
  }) => {
    try {
      const questions = await dhbcDA.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );
      console.log('🚀 ~ questions:', questions);
      if (questions.length === 0) {
        return {
          questions: [],
        };
      }
      return {
        questions,
      };
    } catch (error) {
      console.error('[Redux] Error loading questions:', error);
      // Fallback to local data
      const fallbackData = initializeFallbackData();
      return {
        questions: [],
      };
    }
  },
);

export {loadGameConfig, loadGameQuestions};
