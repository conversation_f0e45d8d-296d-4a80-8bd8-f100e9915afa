import React, {useEffect, useRef, useState, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Image,
  ActivityIndicator,
  Alert,
  Vibration,
} from 'react-native';
import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import store, {RootState} from '../../../redux/store/store';
import {useDhbcHook} from './redux/hooks/dhbcHook';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CardTitleGame} from '../components/CardTitleGame';
import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
// import HintModel from '../components/HintModel'; // Comment: Temporarily disabled
import GameOverModal from '../components/GameOverModel';
import NoDataScreen from './components/NoDataScreen';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useNavigation, useRoute} from '@react-navigation/native';
import {randomGID} from '../../../utils/Utils';
import {DataController} from '../../../base/baseController';
import {GameDA} from '../gameDA';
import HintModel from '../components/HintModel';
import ModelDoneLevel from '../components/ModelDoneLevel';
import ModelDescriptionQuestion from '../components/ModelDescriptionQuestion';
import {hasMaxSort} from '../utils/functions';
import {CardText} from '../components/CardText';
import WinnerModal from '../sakuchuyencanh/components/WinnerModel';

const GameScreen = () => {
  const dhbcHook = useDhbcHook();
  const gameHook = useGameHook();
  const navigation = useNavigation<any>();
  const route = useRoute<any>();

  // Get route params
  const {competenceId = '5', milestoneId = 1} = route.params || {};

  const {totalLives, currentLives, isGameOver, messageGameOver, gem} =
    useSelector((state: RootState) => state.gameStore);
  const {
    dataListQuestion,
    currentLevel,
    totalQuestion,
    questionDone,
    listQuestion,
    currentQuestion,
    loading,
    configLoading,
    error,
    configError,
    initialized,
    configInitialized,
    noData,
    usedHints,
    configLv1,
    configLv2,
    configLv3,
  } = useSelector((state: RootState) => state.dhbcStore);

  const [isShowKeyboard, setIsShowKeyboard] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);
  const [isCorrect, setIsCorrect] = useState<boolean>(false);
  const [answer, setAnswer] = useState<string>('');
  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  const [isWinnerQuestion, setIsWinnerQuestion] = useState<boolean>(false);
  const [isWinnerLevel, setIsWinnerLevel] = useState<boolean>(false);
  const [isWinnerGame, setIsWinnerGame] = useState<boolean>(false);

  const hiddenInputRef = useRef<TextInput | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasHint = currentQuestion?.hint && currentQuestion.hint.trim() !== '';
  const isHintUsed = usedHints.includes(currentQuestion?.id?.toString() || '');
  const shouldShowHintButton = Boolean(hasHint && !isHintUsed);

  // Load game data on component mount
  useEffect(() => {
    initData();

    // Setup keyboard listeners
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsShowKeyboard(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsShowKeyboard(false);
        hiddenInputRef.current?.blur();
      },
    );

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
      // Cleanup timeout on unmount
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Start game when data is loaded
  useEffect(() => {
    if (initialized && configInitialized && !loading && !configLoading) {
      if (!noData) {
        startGame();
      }
    }
  }, [initialized, configInitialized, loading, configLoading]);

  useEffect(() => {
    if (dataListQuestion.length > 0) {
      startGame();
    }
  }, [dataListQuestion]);

  useEffect(() => {
    gameHook.setData({
      stateName: 'time',
      value: getCurrentConfig()?.timeLimit || 0,
    });
  }, [currentLevel]);

  const initData = () => {
    console.log('[StartDHBC] Loading game config and questions...');

    // Load game config first
    dhbcHook.getGameConfig(ConfigAPI.gameDHBC);

    // // Load game questions
    dhbcHook.loadQuestions(ConfigAPI.gameDHBC, milestoneId, competenceId);
    fetchScore();
  };

  const resetData = () => {
    setIsError(false);
    setIsCorrect(false);
    setAnswer('');
    setIsShowKeyboard(false);
    setShowModelConfirm(false);
    setShowHintModel(false);
    setIsWinnerQuestion(false);
    setIsWinnerLevel(false);
    setIsWinnerGame(false);
  };
  const fetchScore = async () => {
    try {
      // Lấy thông tin điểm từ bảng GameCUstomer
      const gameDa = new GameDA();
      const result = await gameDa.getScoreByCustomerIdAndGameId(
        store.getState().customer.data.Id,
        ConfigAPI.gameDHBC,
      );
      gameHook.setData({stateName: 'gem', value: result ?? 0});
    } catch (error) {
      console.error('Lỗi khi lấy thông tin điểm:', error);
    }
  };

  const startGame = useCallback(() => {
    resetQuestion();
    gameHook.restartGame();
    dhbcHook.startGame();
  }, [dhbcHook, gameHook]);

  // lấy config hiện tại
  const getCurrentConfig = () => {
    switch (currentLevel) {
      case 1:
        return configLv1;
      case 2:
        return configLv2;
      case 3:
        return configLv3;
      default:
        break;
    }
  };

  const getBonus = () => {
    const gameConfig = getCurrentConfig();
    return gameConfig?.score || 0;
  };

  // Define functions first
  const showKeyboard = () => {
    if (hiddenInputRef.current) {
      hiddenInputRef.current.focus();
    }
  };

  const nextQuestion = () => {
    gameHook.continueGame();
    setIsWinnerQuestion(false);
  };

  const onNextLevel = () => {
    gameHook.continueGame();
    setIsWinnerLevel(false);
    resetData();
    dhbcHook.nextLevel();
  };

  const onWinnerQuestion = () => {
    gameHook.pauseGame();
    setIsWinnerQuestion(true);
  };

  const onWinnerLevel = () => {
    gameHook.pauseGame();
    setIsWinnerLevel(true);
  };

  const onWinnerGame = () => {
    gameHook.pauseGame();
    setIsWinnerGame(true);
  };

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // Reset câu hỏi và gợi ý
  const resetQuestion = () => {
    setIsCorrect(false);
    setIsError(false);
    setAnswer('');
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    setIsError(false);
    setIsCorrect(false);

    // Null check cho currentQuestion và answer
    if (!currentQuestion || !currentQuestion.answer) {
      console.warn('[StartDHBC] Current question or answer is missing');
      return;
    }

    if (answer.toLowerCase() === currentQuestion.answer.toLowerCase()) {
      setIsCorrect(true);
      if (hasMaxSort(listQuestion, currentQuestion)) {
        if (currentLevel === 3) {
          return onWinnerGame();
        } else {
          return onWinnerLevel();
        }
      }

      if (timeoutRef.current) {
        // Clear previous timeout if exists
        clearTimeout(timeoutRef.current);
      }

      onWinnerQuestion();

      // Set new timeout with cleanup
      timeoutRef.current = setTimeout(() => {
        resetQuestion();
        dhbcHook.setData({stateName: 'questionDone', value: questionDone + 1});
        dhbcHook.nextQuestion();
        timeoutRef.current = null;
      }, 2000);
    } else {
      // Rung thiết bị khi trả lời sai
      Vibration.vibrate([0, 500, 200, 500]);

      setIsError(true);
      gameHook.setData({stateName: 'currentLives', value: currentLives - 1});
    }
  };

  // Sử dụng gợi ý - Comment: Tạm thời disable vì không có hint từ API
  const useHint = () => {
    const gameConfig = getCurrentConfig();
    if (gem < (gameConfig?.gemHint || 10)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ gem để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gameConfig?.gemHint || 0),
    });
    setShowModelConfirm(false);

    updateScore(gameConfig?.gemHint || 0);

    setShowHintModel(true);
    console.log('Hint feature temporarily disabled - no hint data from API');
  };

  const updateScore = async (score: number) => {
    if (score <= 0) return;
    const gameConfig = getCurrentConfig();
    const gamecustomerController = new DataController('GameCustomer');
    const customerId = store.getState().customer.data.Id;
    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameDHBC,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: -(gameConfig?.gemHint || 0),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - DHBC_${milestoneId}`,
    };

    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    setShowHintModel(true);
    return true;
  };

  // Retry loading data
  const retryLoadData = () => {
    dhbcHook.getGameConfig(ConfigAPI.gameDHBC);
    dhbcHook.loadQuestions(ConfigAPI.gameDHBC, milestoneId, competenceId);
  };

  // Go back to previous screen
  const goBack = () => {
    navigation.goBack();
  };

  // Show loading screen
  if (loading || configLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <View style={styles.loadingContent}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Đang tải dữ liệu game...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show no data screen
  if (noData || error || configError) {
    return (
      <NoDataScreen
        message={
          error || configError || 'Không có dữ liệu câu hỏi cho game này'
        }
        onRetry={retryLoadData}
        onGoBack={goBack}
      />
    );
  }

  return (
    <SafeAreaView style={{flex: 1}}>
      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          isShowSuggest={shouldShowHintButton}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
          gameId={ConfigAPI.gameDHBC}
        />
        {!isShowKeyboard ? (
          <View>
            <LineProgressBar
              progress={
                totalQuestion > 0 ? (questionDone / totalQuestion) * 100 : 0
              }></LineProgressBar>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
              <CardText text={`Cấp độ ${currentLevel}`} bgColor="#1BDB55" />
              <CountBadge
                current={questionDone}
                total={totalQuestion}></CountBadge>
            </View>
            <View style={{marginTop: 16}}>
              <CardTitleGame
                title={currentQuestion.title || ''}></CardTitleGame>
            </View>
          </View>
        ) : null}
        {/* Game Content */}
        <View style={styles.gameContent}>
          {/* Emoji Game Card */}
          <Image
            source={{
              uri: currentQuestion?.image || '',
            }}
            style={styles.imageContainer}
            resizeMode="cover"
            onError={imageError => {
              console.warn(
                '[StartDHBC] Image load error:',
                imageError.nativeEvent.error,
              );
            }}
          />

          {/* Answer */}
          <View
            style={[
              styles.answerContainer,
              isError ? styles.answerContainerError : null,
              isCorrect ? styles.answerContainerCorrect : null,
            ]}>
            {answer.length < 1 ? (
              <Text style={styles.placeholderText}>Nhập đáp án tại đây</Text>
            ) : (
              <Text style={styles.answerText}>{answer}</Text>
            )}
            {isCorrect && (
              <Text style={styles.correctText}>Đáp án chính xác</Text>
            )}
            {isError && (
              <Text style={styles.errorText}>Sai rồi, hãy thử đáp án khác</Text>
            )}
            <TextInput
              ref={hiddenInputRef}
              style={styles.hiddenInput}
              value={answer}
              onChangeText={text => setAnswer(text)}
              autoCapitalize="none"
              autoCorrect={false}
              spellCheck={false}
              caretHidden={true}
            />
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.skipButton} onPress={showKeyboard}>
              <Text style={styles.skipButtonText}>Nhập đáp án</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.checkButton} onPress={checkAnswer}>
              <Text style={styles.checkButtonText}>Kiểm tra</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom */}
        <View>
          <BottomGame
            resetGame={startGame}
            backGame={() => {
              navigation.goBack();
            }}
            pauseGame={() => {
              // xử lý pause game
              gameHook.pauseGame();
            }}
            volumeGame={() => {}}
          />
        </View>
      </View>
      <View style={styles.modalContainer}>
        <ModelConfirm
          isShow={showModelConfirm}
          closeModal={() => setShowModelConfirm(false)}
          onConfirm={useHint}
          message={`Bạn sẽ bị trừ ${
            getCurrentConfig()?.gemHint || 0
          } điểm khi sử dụng trợ giúp này`}
        />
        {/* Comment: Tạm thời comment hint vì không có field hint từ API */}
        <HintModel
          isShow={showHintModel}
          closeModal={() => setShowHintModel(false)}
          text={currentQuestion.hint}
        />
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={startGame}
          message={messageGameOver}
          isTimeOut={false}
        />
        <ModelDescriptionQuestion
          visible={isWinnerQuestion}
          onNext={nextQuestion}
          message={currentQuestion?.description || ''}
        />
        <ModelDoneLevel
          visible={isWinnerLevel}
          message={`Bạn đã vượt qua cấp ${currentLevel}`}
          onNextLevel={onNextLevel}
          gameId={ConfigAPI.gameDHBC}
          competenceId={competenceId}
          totalScore={getBonus()}
        />
        <WinnerModal
          visible={isWinnerGame}
          onClose={() => {}}
          restartGame={startGame}
          competenceId={competenceId}
          totalScore={getBonus()}
          gameId={ConfigAPI.gameDHBC}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    margin: 16,
  },
  gameContent: {
    marginTop: 32,
    flex: 1,
    alignItems: 'center',
  },
  // Loading styles
  loadingContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  // Placeholder text style
  placeholderText: {
    color: '#999',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  // Modal container style
  modalContainer: {
    zIndex: 1000,
  },

  imageContainer: {
    width: '90%',
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
  },

  answerContainer: {
    position: 'relative',
    marginTop: 32,
    maxWidth: '70%',
    minWidth: 200,
    backgroundColor: 'white',
    borderRadius: 15,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  answerContainerError: {
    borderColor: '#FF6B6B',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerContainerCorrect: {
    borderColor: '#2EB553',
    borderWidth: 3,
    borderRadius: 12,
  },
  answerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#FCF8E8',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginTop: 5,
  },
  correctText: {
    fontSize: 12,
    textAlign: 'center',

    backgroundColor: '#E8F8FC',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 12,
    color: '#2EB553',
    fontWeight: 'bold',
    marginTop: 5,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  skipButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginRight: 10,
  },
  skipButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  checkButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 25,
    paddingVertical: 15,
    borderRadius: 15,
    flex: 1,
    marginLeft: 10,
  },
  checkButtonText: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
    fontSize: 16,
  },
  hiddenInput: {
    width: 0, // Chiều rộng 0
    height: 0, // Chiều cao 0
    opacity: 0, // Hoàn toàn trong suốt
    position: 'absolute', // Không chiếm không gian trong layout
    // Để đảm bảo nó thực sự không nhìn thấy và không tương tác ngoài ý muốn:
    top: -10000, // Đẩy ra rất xa màn hình
    left: -10000,
  },
});

export default GameScreen;
