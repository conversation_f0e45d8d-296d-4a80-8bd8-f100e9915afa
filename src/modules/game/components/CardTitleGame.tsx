import {useEffect, useState, useCallback} from 'react';
import {Text, View, StyleSheet, Image, TouchableOpacity} from 'react-native';
import Sound from 'react-native-sound';

interface CardTitleGameProps {
  title: string;
  showIcon?: boolean;
  audioUrl: string | null;
}

export const CardTitleGame = ({
  title,
  showIcon,
  audioUrl,
}: CardTitleGameProps) => {
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);

  // Dọn dẹp audio
  const cleanupAudio = useCallback(() => {
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
      setAudioPlayer(null);
    }
  }, [audioPlayer]);

  // Thiết lập audio
  const setupAudio = useCallback(
    (url: string) => {
      console.log(11111);
      cleanupAudio();
      Sound.setCategory('Playback');

      const sound = new Sound(url, '', error => {
        if (error) {
          console.error('Failed to load audio:', error);
          return;
        }
        console.log('===== SETTING UP AUDIO =====');
        console.log('Audio URL:', url);
        setAudioPlayer(sound);
      });
    },
    [cleanupAudio],
  );

  // Dừng audio
  const stopAudio = useCallback(() => {
    if (audioPlayer && isPlayingAudio) {
      audioPlayer.stop(() => {
        setIsPlayingAudio(false);
      });
    }
  }, [audioPlayer, isPlayingAudio]);

  // Phát audio
  const playAudio = useCallback(() => {
    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      stopAudio();
    } else {
      setIsPlayingAudio(true);
      console.log('===== PLAY AUDIO =====');
      audioPlayer.play(success => {
        setIsPlayingAudio(false);
        if (!success) {
          console.error('Audio playback failed');
        }
      });
    }
  }, [audioPlayer, isPlayingAudio, stopAudio]);

  useEffect(() => {
    if (audioUrl) {
      setupAudio(audioUrl);
    }
    return cleanupAudio;
  }, [audioUrl]);

  return (
    <View style={styles.container}>
      {showIcon && audioUrl && (
        <TouchableOpacity style={styles.audioContainer} onPress={playAudio}>
          <Text style={styles.audioIcon}>{isPlayingAudio ? '⏸️' : '🔊'}</Text>
        </TouchableOpacity>
      )}
      <View style={styles.instruction}>
        <Text style={styles.wordText}>{title || ''}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FCF8E8',
    padding: 12,
    borderRadius: 12,
  },
  instruction: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  wordText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
  },
  audioContainer: {
    height: '100%',
    marginRight: 8,
  },
  audio: {
    width: 26,
    height: 26,
  },
  audioIcon: {
    fontSize: 16,
    color: '#333',
  },
});
