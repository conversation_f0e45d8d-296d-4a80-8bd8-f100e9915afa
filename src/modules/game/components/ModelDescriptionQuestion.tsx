import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  ImageBackground,
} from 'react-native';

const {width} = Dimensions.get('window');

interface ModelDescriptionQuestionProps {
  visible: boolean;
  message: string;
  onNext: () => void;
}

const ModelDescriptionQuestion = ({
  visible,
  message,
  onNext,
}: ModelDescriptionQuestionProps) => {
  return (
    <Modal visible={visible} transparent={true} animationType="fade">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <ImageBackground
            style={{
              width: width * 0.93,
              height: 200,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            source={require('../assets/description.png')}>
            <Text style={styles.messageText}>{message}</Text>
          </ImageBackground>

          <Text
            style={{
              fontSize: 24,
              fontWeight: 'bold',
              marginVertical: 20,
              color: '#FDE832',
            }}>
            Chính xác
          </Text>

          {/* Hình ảnh con chim */}
          <Image source={require('../assets/bird_win_question.png')} />
          {/* Nút restart */}
          {/* thêm background image cho nút start */}
          <TouchableOpacity style={styles.restartButton} onPress={onNext}>
            <Image
              source={require('../assets/next_button.png')}
              style={styles.restartButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    width: width * 0.93,
    padding: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 20,
    fontFamily: 'BagelFatOne-Regular',
    marginRight: 20,
  },
  restartButton: {
    width: 230,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ModelDescriptionQuestion;
