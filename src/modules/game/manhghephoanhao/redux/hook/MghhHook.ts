import {useDispatch} from 'react-redux';
import { startGame, setData, nextQuestion, nextLevel, restartLevel, restartGame} from '../MGHHReducer';
import { loadInitData } from '../MGHHAsyncThunk';

export const useMghhHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    nextLevel: () => {
      dispatch(nextLevel());
    },
    restartLevel: () => {
      dispatch(restartLevel());
    },
    restartGame: () => {
      dispatch(restartGame());
    },
    loadInitData: ({gameId, stage, competenceId}: {gameId: string, stage: number, competenceId: string}) => {
      dispatch(loadInitData({gameId, stage, competenceId}) as any);
    },
  };

  return action;
};
