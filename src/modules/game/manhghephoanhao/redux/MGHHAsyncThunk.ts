import {createAsyncThunk} from '@reduxjs/toolkit';
import {GetGameConfigRequest} from '../../sakutimban/types/sakuTBTypes';
import {MghhDa} from '../da/MghhDa';
import {initializeFallbackData} from '../../sakutimban/data/fallbackData';

const mghhDa = new MghhDa();

const loadInitData = createAsyncThunk(
  'MGHHReducer/loadInitData',
  async ({gameId, stage,
    competenceId,}: {
      gameId: string;
      stage: number;
      competenceId: string;
    }) => {
    const gameConfig = await MghhDa.getGameConfig(gameId);
    const questions = await mghhDa.getQuestionsByGameAndStage(
        gameId,
        stage,
        competenceId,
      );
    return {
      gameConfig,
      questions,
    };
  },
);

export {loadInitData};
