import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
  Easing,
  ImageBackground,
  ActivityIndicator,
  Alert,
} from 'react-native';

import CountBadge from '../components/CountQuestions';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSelector} from 'react-redux';
import {RootState} from '../../../redux/store/store';

import {SafeAreaView} from 'react-native-safe-area-context';

import {BottomGame} from '../components/BottomGame';
import ModelConfirm from '../components/ModelConfirm';
import HintModel from '../components/HintModel';
import GameOverModal from '../components/GameOverModel';
import ModelDoneLevel from '../components/ModelDoneLevel';

import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {
  DraggableWordInSentence,
  DraggableWord,
} from './components/draggableWords';
import {InsertZone} from './components/insertView';
import {useNavigation, useRoute} from '@react-navigation/native';
import ConfigAPI from '../../../Config/ConfigAPI';

import {useSakuXTHook} from '../../../redux/hook/game/sakuXTHook';
import WinnerModal from './components/WinnerModal';
import Sound from 'react-native-sound';
import {SakuXTWord} from './types/sakuXTTypes';
import {ColorThemes} from '../../../assets/skin/colors';
import {DataController} from '../../../base/baseController';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {randomGID} from '../../../utils/Utils';
import {GameDA} from '../gameDA';
import {set} from 'date-fns';
import GamePauseModal from '../components/ModelPauseGame';

const {width: SCREEN_WIDTH} = Dimensions.get('window');

// Constants
const DEFAULT_SENTENCE = ['Tôi', 'ăn', 'cơm'];
const DEFAULT_DRAGGABLE_WORDS = ['đang', 'sẽ', 'đã', 'rất', 'ngon'];
const DETECTION_RADIUS = 100;
const MAX_SENTENCE_LENGTH = 10;
const SENTENCE_CONTAINER_HEIGHT = {
  NORMAL: 80,
  EXPANDED: 110,
};
const ANIMATION_DURATION = {
  FAST: 250,
  NORMAL: 300,
};

interface InsertZonePosition {
  x: number;
  y: number;
  index: number;
  distance?: number;
}

interface DragState {
  word: string;
  index: number;
  uniqueId: string;
}

const SakuXayTo = () => {
  const sakuXTHook = useSakuXTHook();
  const gameHook = useGameHook();
  const {isGameOver, messageGameOver, gem} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {
    availableWords,
    wordsInDropZone,
    wordsOutSideZone,
    questionDone,
    totalQuestion,
    currentQuestion,
    loading,
    error,
    configLoading,
    configError,
    initialized,
    configInitialized,
    // Sử dụng config data từ API
    maxLives,
    currentLives,
    timeLimit,
    isAnswerCorrect,
    timeRemaining,
    feedbackMessage,
    feedbackType,
    gameConfig,
  } = useSelector((state: RootState) => state.SakuXT);
  const navigation = useNavigation<any>();

  const [showModelConfirm, setShowModelConfirm] = useState<boolean>(false);
  const [showHintModel, setShowHintModel] = useState<boolean>(false);
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const [sentence, setSentence] = useState<string[]>([]);
  const [draggableWords, setDraggableWords] = useState<string[]>([]);

  const [usedWords, setUsedWords] = useState<Set<string>>(new Set());
  const [activeZoneIndex, setActiveZoneIndex] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isInserting, setIsInserting] = useState(false);
  const [insertingAtIndex, setInsertingAtIndex] = useState<number>(-1);
  const [draggingWordFromSentence, setDraggingWordFromSentence] =
    useState<DragState | null>(null);
  const [currentlyDraggingWord, setCurrentlyDraggingWord] = useState<
    string | null
  >(null);
  const insertZoneRefs = useRef<View[]>([]);
  const insertZonesRef = useRef<InsertZonePosition[]>([]);
  const currentSentenceRef = useRef<string[]>(sentence);

  // Animation refs for sentence expansion
  const sentenceContainerHeight = useRef(
    new Animated.Value(SENTENCE_CONTAINER_HEIGHT.NORMAL),
  ).current;

  //router param
  const route = useRoute<any>();
  const {competenceId, milestoneId} = route.params || {competenceId: '1'};

  const [audioPlayer, setAudioPlayer] = useState<Sound | null>(null);
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const [isPauseGame, setIsPauseGame] = useState(false);

  // Load game data on component mount
  useEffect(() => {
    initializeGameData();
    return () => {
      sakuXTHook.reset();
      gameHook.resetGame();
    };
  }, [competenceId, milestoneId]);

  const updateSentenceAndDraggableWords = useCallback(() => {
    if (
      initialized &&
      wordsInDropZone.length > 0 &&
      wordsOutSideZone.length > 0
    ) {
      const sentence = wordsInDropZone.map(word => word?.text);
      const draggableW = wordsOutSideZone.map(word => word?.text);
      setSentence([...sentence]);
      setDraggableWords([...draggableW]);
      //init wordsInDropZone
      console.log('init sentence', sentence);
      console.log('init draggableW', draggableW);
    }
  }, [initialized, wordsInDropZone.length, wordsOutSideZone.length]);

  useEffect(() => {
    updateSentenceAndDraggableWords();
  }, [updateSentenceAndDraggableWords]);

  useEffect(() => {
    // Sử dụng config từ API nếu có, fallback về default values
    if (gameConfig) {
      gameHook.setData({stateName: 'totalLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'currentLives', value: gameConfig.maxLives});
      gameHook.setData({stateName: 'time', value: gameConfig.timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'isGameOver', value: false});
      gameHook.setData({
        stateName: 'gemCost',
        value: gameConfig?.gemHint || 10,
      });
    } else {
      // Fallback to default values
      gameHook.restartGame();
    }
  }, [gameConfig]);

  const initializeGameData = async () => {
    try {
      // Initialize game
      setSentence([]);
      setDraggableWords([]);
      console.log('===== STARTING initializeGameData =====');
      console.log('GameId:gameSKXT', ConfigAPI.gameSKXT);
      console.log('MilestoneId:', milestoneId);
      console.log('CompetenceId:', competenceId);

      // Load game config
      console.log('===== CALLING loadGameConfig =====');
      await sakuXTHook.loadGameConfig(ConfigAPI.gameSKXT);
      console.log('===== loadGameConfig COMPLETED =====');
      // Load questions
      console.log('===== CALLING loadQuestions =====');
      await sakuXTHook.loadQuestions(
        ConfigAPI.gameSKXT,
        milestoneId,
        competenceId,
      );
      console.log('===== loadQuestions COMPLETED =====');

      // Initialize game
      console.log('===== CALLING initializeGame =====');
      sakuXTHook.initializeGame();
      console.log('===== initializeGame COMPLETED =====');
      gameHook.getCurrentScore(ConfigAPI.gameSKXT);
    } catch (err) {
      console.error('===== ERROR in initializeGameData:', err);
    }
  };

  // Update sentence ref when sentence changes
  React.useEffect(() => {
    currentSentenceRef.current = sentence;
  }, [sentence]);

  const updateInsertZonePositions = useCallback(() => {
    const newZones: InsertZonePosition[] = [];
    let completed = 0;

    insertZoneRefs.current.forEach((ref, index) => {
      if (ref) {
        ref.measure((_, __, width, height, pageX, pageY) => {
          const centerX = pageX + width / 2;
          const centerY = pageY + height / 2;

          newZones[index] = {
            x: centerX,
            y: centerY,
            index,
          };
          completed++;

          if (completed === insertZoneRefs.current.length) {
            insertZonesRef.current = newZones;
          }
        });
      }
    });
  }, []);

  // Update positions when component mounts and when sentence changes
  React.useLayoutEffect(() => {
    const timer = setTimeout(() => {
      updateInsertZonePositions();
    }, 100);
    return () => clearTimeout(timer);
  }, [sentence, updateInsertZonePositions]);

  const handleInsert = useCallback(
    (index: number, word: string) => {
      if (usedWords.has(word) || sentence.length >= MAX_SENTENCE_LENGTH) {
        return;
      }

      setSentence(prevSentence => {
        const newSentence = [...prevSentence];
        newSentence.splice(index, 0, word);
        return newSentence;
      });

      setUsedWords(prev => new Set([...prev, word]));
      setDraggableWords(prev => prev.filter(w => w !== word));
    },
    [usedWords, sentence.length],
  );

  const handleDragStart = useCallback(() => {
    setActiveZoneIndex(null);
  }, []);

  // Handle drag start for words in sentence
  const handleSentenceWordDragStart = useCallback(
    (word: string, index: number) => {
      const uniqueId = `${Date.now()}-${Math.random()}`;
      setDraggingWordFromSentence({word, index, uniqueId});
      setCurrentlyDraggingWord(word);
      setActiveZoneIndex(null);
    },
    [],
  );

  // Helper function to find closest zone
  const findClosestZone = useCallback(
    (x: number, y: number, zones: InsertZonePosition[]) => {
      return zones.reduce(
        (closest: InsertZonePosition, zone: InsertZonePosition) => {
          const dx = Math.abs(zone.x - x);
          const dy = Math.abs(zone.y - y);
          const distance = Math.sqrt(dx * dx + dy * dy);

          // Give priority to end zone (larger detection area)
          const isEndZone = zone.index === sentence.length;
          const effectiveDistance = isEndZone ? distance * 0.8 : distance;

          return effectiveDistance < (closest.distance || Infinity)
            ? {...zone, distance: effectiveDistance}
            : closest;
        },
        {x: 0, y: 0, index: 0, distance: Infinity},
      );
    },
    [sentence.length],
  );

  // Helper function to reset drag states
  const resetDragStates = useCallback(() => {
    setDraggingWordFromSentence(null);
    setCurrentlyDraggingWord(null);
    setActiveZoneIndex(null);
    setIsInserting(false);
    setInsertingAtIndex(-1);
  }, []);

  // Helper function to find word position in sentence
  const findWordPosition = useCallback(
    (word: string, originalIndex: number, currentSentence: string[]) => {
      // First, try the original index if it's still valid
      if (
        originalIndex >= 0 &&
        originalIndex < currentSentence.length &&
        currentSentence[originalIndex] === word
      ) {
        return originalIndex;
      }

      // If original index doesn't work, find the word in the sentence
      return currentSentence.findIndex(w => w === word);
    },
    [],
  );

  // Handle drag end for words in sentence
  const handleSentenceWordDragEnd = useCallback(
    (word: string, fromIndex: number, x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) {
        setDraggingWordFromSentence(null);
        return;
      }

      const closestZone = findClosestZone(x, y, zones);

      if (closestZone.distance && closestZone.distance < DETECTION_RADIUS) {
        let targetIndex = closestZone.index;

        // Only move if target position is different from current position
        if (targetIndex !== fromIndex && targetIndex !== fromIndex + 1) {
          const currentSentence = currentSentenceRef.current;
          const actualFromIndex = findWordPosition(
            word,
            fromIndex,
            currentSentence,
          );

          if (actualFromIndex === -1) {
            resetDragStates();
            return;
          }

          // Use the actual current position
          fromIndex = actualFromIndex;

          if (sentence[fromIndex] !== word) {
            return sentence; // Don't change anything
          }

          const newSentence = [...sentence];

          // Remove word from current position
          const removedWord = newSentence.splice(fromIndex, 1)[0];

          // Adjust target index if it's after the removed word
          if (targetIndex > fromIndex) {
            targetIndex -= 1;
          }

          // Validate target index
          if (targetIndex < 0) targetIndex = 0;
          if (targetIndex > newSentence.length)
            targetIndex = newSentence.length;

          // Insert word at new position
          newSentence.splice(targetIndex, 0, removedWord);

          setSentence(newSentence);

          // Reset drag states immediately after successful move
          resetDragStates();

          // Update insert zone positions after sentence change
          setTimeout(() => {
            updateInsertZonePositions();
          }, 100);
        }
      } else {
        // Check if dragged outside sentence area - remove from sentence
        const sentenceArea = {
          x: 50,
          y: 400,
          width: SCREEN_WIDTH - 100,
          height: 200,
        };

        const isOutsideSentenceArea =
          x < sentenceArea.x ||
          x > sentenceArea.x + sentenceArea.width ||
          y < sentenceArea.y ||
          y > sentenceArea.y + sentenceArea.height;

        if (isOutsideSentenceArea) {
          const currentSentence = currentSentenceRef.current;
          const actualFromIndex = findWordPosition(
            word,
            fromIndex,
            currentSentence,
          );

          if (
            actualFromIndex >= 0 &&
            actualFromIndex < currentSentence.length
          ) {
            setSentence(prevSentence => {
              const newSentence = [...prevSentence];
              newSentence.splice(actualFromIndex, 1);
              return newSentence;
            });
            setDraggableWords(prev => [...prev, word]);
            setUsedWords(prev => {
              const newUsed = new Set(prev);
              newUsed.delete(word);
              return newUsed;
            });

            resetDragStates();

            // Update insert zone positions after sentence change
            setTimeout(() => {
              updateInsertZonePositions();
            }, 100);
          }
        }
      }

      // Reset states immediately for cases where no movement occurred
      if (!closestZone.distance || closestZone.distance >= DETECTION_RADIUS) {
        resetDragStates();
      }
    },
    [
      SCREEN_WIDTH,
      updateInsertZonePositions,
      findClosestZone,
      findWordPosition,
      resetDragStates,
    ],
  );

  const handleDragMove = useCallback(
    (x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = findClosestZone(x, y, zones);

      if (closestZone.distance && closestZone.distance < DETECTION_RADIUS) {
        let targetIndex = closestZone.index;

        if (draggingWordFromSentence) {
          const currentSentence = currentSentenceRef.current;
          const currentFromIndex = currentSentence.findIndex(
            w => w === draggingWordFromSentence.word,
          );

          if (currentFromIndex !== -1) {
            // Don't highlight the same position or adjacent positions for smoother UX
            if (
              targetIndex === currentFromIndex ||
              targetIndex === currentFromIndex + 1
            ) {
              if (activeZoneIndex !== null) {
                setActiveZoneIndex(null);
                setIsInserting(false);
                setInsertingAtIndex(-1);

                Animated.timing(sentenceContainerHeight, {
                  toValue: SENTENCE_CONTAINER_HEIGHT.NORMAL,
                  duration: ANIMATION_DURATION.FAST,
                  easing: Easing.out(Easing.cubic),
                  useNativeDriver: false,
                }).start();
              }
              return;
            }
          }
        }

        // Smooth transition to new target
        if (activeZoneIndex !== targetIndex) {
          setActiveZoneIndex(targetIndex);

          if (targetIndex <= sentence.length) {
            setIsInserting(true);
            setInsertingAtIndex(targetIndex);

            Animated.timing(sentenceContainerHeight, {
              toValue: SENTENCE_CONTAINER_HEIGHT.EXPANDED,
              duration: ANIMATION_DURATION.NORMAL,
              easing: Easing.out(Easing.cubic),
              useNativeDriver: false,
            }).start();
          }
        }
      } else {
        // Smooth transition out of any highlight
        if (activeZoneIndex !== null) {
          setActiveZoneIndex(null);
          setIsInserting(false);
          setInsertingAtIndex(-1);

          Animated.timing(sentenceContainerHeight, {
            toValue: SENTENCE_CONTAINER_HEIGHT.NORMAL,
            duration: ANIMATION_DURATION.NORMAL,
            easing: Easing.out(Easing.cubic),
            useNativeDriver: false,
          }).start();
        }
      }
    },
    [
      activeZoneIndex,
      sentence.length,
      sentenceContainerHeight,
      draggingWordFromSentence,
      findClosestZone,
    ],
  );

  const handleDragEnd = useCallback(
    (word: string, x: number, y: number) => {
      const zones = insertZonesRef.current;

      if (zones.length === 0) return;

      const closestZone = findClosestZone(x, y, zones);

      if (closestZone.distance && closestZone.distance < 50) {
        if (usedWords.has(word)) return;

        setSentence(prevSentence => {
          const newSentence = [...prevSentence];
          newSentence.splice(closestZone.index, 0, word);
          return newSentence;
        });

        setUsedWords(prev => new Set([...prev, word]));
        setDraggableWords(prev => prev.filter(w => w !== word));
      }

      setActiveZoneIndex(null);
      setIsInserting(false);
      setInsertingAtIndex(-1);

      // Reset sentence container height
      Animated.timing(sentenceContainerHeight, {
        toValue: SENTENCE_CONTAINER_HEIGHT.NORMAL,
        duration: ANIMATION_DURATION.NORMAL,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: false,
      }).start();
    },
    [usedWords, sentenceContainerHeight, findClosestZone],
  );

  const handleSubmit = useCallback(() => {
    // Kiểm tra đáp án
    console.log('===== CHECK ANSWER CALLED =====');

    //add sentence into wordsInDropZone
    if (availableWords.length > 0 && initialized == true) {
      const wordsInzone = Array<SakuXTWord>();
      if (sentence.length > 0) {
        for (let i = 0; i < sentence.length; i++) {
          wordsInzone.push(
            availableWords.find(
              word =>
                word.text.normalize().trim().toLowerCase() ===
                sentence[i].normalize().trim().toLowerCase(),
            )!,
          );
        }
      }
      console.log('wordsInzone', wordsInzone);
      sakuXTHook.setData({
        stateName: 'wordsInCheck',
        value: [...wordsInzone],
      });
    }

    // Check answer using Redux action
    sakuXTHook.checkAnswer();

    // Đợi 100ms để Redux state update
  }, [sentence.length]);

  useEffect(() => {
    setTimeout(() => {
      sakuXTHook.clearFeedback();
      setIsSubmitted(false);
    }, 2000);
  }, [feedbackType]);

  // Auto next question when answer is correct
  useEffect(() => {
    if (isAnswerCorrect === true) {
      if (questionDone + 1 >= totalQuestion && totalQuestion > 0) {
        setTimeout(() => {
          setShowWinnerModal(true);
          gameHook.setData({stateName: 'isRunTime', value: false}); // Dừng timer
        }, 500); // Delay nhỏ để animation hoàn thành
      }

      setTimeout(() => {
        sakuXTHook.nextQuestion();
        sakuXTHook.clearFeedback();
      }, 1000); // Show success message for 1.5 seconds
    }
  }, [isAnswerCorrect]);

  // Start timer when game is initialized and config is loaded
  useEffect(() => {
    if (initialized && configInitialized) {
      console.log('===== STARTING TIMER =====');
      sakuXTHook.startTimer();
      gameHook.setData({stateName: 'isRunTime', value: true});
      gameHook.setData({stateName: 'time', value: timeRemaining});
    }
  }, [initialized, configInitialized]);

  // Timer countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (initialized && configInitialized && timeRemaining > 0 && !isPauseGame) {
      interval = setInterval(() => {
        sakuXTHook.updateTimer();
        // Sync with global game state for HeadGame component
        gameHook.setData({stateName: 'time', value: timeRemaining - 1});
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [initialized, configInitialized, timeRemaining, isPauseGame]);

  // Game over when time runs out
  useEffect(() => {
    if (timeRemaining <= 0 && initialized) {
      gameOver('Hết giờ rồi, làm lại nào');
    }
  }, [timeRemaining]);

  const restartGame = () => {
    sakuXTHook.setData({stateName: 'wordsInDropZone', value: []});
    sakuXTHook.setData({stateName: 'wordsOutSideZone', value: []});
    // Reset game state
    setIsSubmitted(false);
    setUsedWords(new Set());
    // Reset animation states
    setIsInserting(false);
    setInsertingAtIndex(-1);
    setActiveZoneIndex(null);
    setDraggingWordFromSentence(null);
    setCurrentlyDraggingWord(null);
    resetDragStates();

    // Reset sentence container height
    sentenceContainerHeight.setValue(SENTENCE_CONTAINER_HEIGHT.NORMAL);
    sakuXTHook.clearFeedback();
    sakuXTHook.reset();
    gameHook.restartGame();
    updateSentenceAndDraggableWords();
    if (configInitialized && timeLimit) {
      console.log(
        `[StartSakuXT] Syncing global timer with SakuXT config: ${timeLimit}s`,
      );
      gameHook.setData({stateName: 'time', value: timeLimit});
      gameHook.setData({stateName: 'isRunTime', value: true});

      // Sync lives với SakuXT config
      if (maxLives) {
        gameHook.setData({stateName: 'totalLives', value: maxLives});
        gameHook.setData({stateName: 'currentLives', value: maxLives});
      }
    }
  };

  useEffect(() => {
    if (currentLives === 0) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  // Thua
  const gameOver = (message: string) => {
    gameHook.gameOver(message);
    sakuXTHook.initializeGame();
  };

  // Sử dụng gợi ý - Comment: Tạm thời disable vì không có hint từ API
  const useHint = () => {
    if (gem < (gameConfig?.gemHint || 5)) {
      // show model thông báo không đủ gem
      Alert.alert(
        'Thông báo',
        'Bạn không đủ gem để sử dụng gợi ý',
        [
          {
            text: 'OK',
            style: 'cancel',
          },
        ],
        {cancelable: false},
      );
      return;
    }
    gameHook.setData({
      stateName: 'gem',
      value: gem - (gameConfig?.gemHint || 5),
    });
    setShowModelConfirm(false);
    updateScore(gameConfig?.gemHint || 0);
    setShowHintModel(true);
    console.log('Hint feature temporarily disabled - no hint data from API');
  };
  const customerId = useSelectorCustomerState().data.Id;
  const updateScore = async (score: number) => {
    if (score <= 0) return;
    const gamecustomerController = new DataController('GameCustomer');

    const game = {
      Id: randomGID(),
      CustomerId: customerId,
      GameId: ConfigAPI.gameSKXT,
      Stage: milestoneId,
      Competency: competenceId,
      Status: 0,
      DateCreated: new Date().getTime(),
      Score: -(gameConfig?.gemHint || 0),
      HighestScore: 0,
      PlayedAt: new Date().getTime(),
      Name: `Sử dụng gợi ý - Sakuxayto_${milestoneId}`,
    };

    console.log('Tạo bản ghi mới:', game);
    const result = await gamecustomerController.add([game]);
    if (result.code !== 200) {
      return false;
    }
    setShowHintModel(true);
    return true;
  };

  // Setup audio when question changes
  useEffect(() => {
    if (currentQuestion?.audioUrl) {
      setupAudio(currentQuestion.audioUrl);
    }

    // Cleanup previous audio
    return () => {
      if (audioPlayer) {
        audioPlayer.stop();
        audioPlayer.release();
      }
    };
  }, [currentQuestion]);

  // Setup audio player
  const setupAudio = (audioUrl: string) => {
    console.log('===== SETTING UP AUDIO =====');
    console.log('Audio URL:', audioUrl);

    // Release previous audio
    if (audioPlayer) {
      audioPlayer.stop();
      audioPlayer.release();
    }

    // Enable playback in silence mode (iOS)
    Sound.setCategory('Playback');

    // Create new audio instance
    const sound = new Sound(audioUrl, '', audioError => {
      if (audioError) {
        console.error('Failed to load audio:', audioError);
        return;
      }
      console.log('Audio loaded successfully');
      setAudioPlayer(sound);
    });
  };
  // Play audio function
  const playAudio = () => {
    console.log('===== PLAY AUDIO CALLED =====');
    console.log('Audio player:', audioPlayer);
    console.log('Is playing:', isPlayingAudio);

    if (!audioPlayer) {
      console.log('No audio player available');
      return;
    }

    if (isPlayingAudio) {
      // Stop audio if currently playing
      audioPlayer.stop(() => {
        console.log('Audio stopped');
        setIsPlayingAudio(false);
      });
    } else {
      // Play audio
      setIsPlayingAudio(true);
      audioPlayer.play(success => {
        console.log('Audio play finished, success:', success);
        setIsPlayingAudio(false);
      });
    }
  };

  const togglePauseGame = () => {
    console.log(
      `[StartSakuLC] Toggle pause game: ${isPauseGame ? 'Resume' : 'Pause'}`,
    );

    if (!isPauseGame) {
      // Pause game
      gameHook.setData({stateName: 'isRunTime', value: false});
      setIsPauseGame(true);

      // Pause audio nếu đang phát
      if (isPlayingAudio && audioPlayer) {
        audioPlayer.pause();
        setIsPlayingAudio(false);
      }
    } else {
      // Resume game
      gameHook.setData({stateName: 'isRunTime', value: true});
      setIsPauseGame(false);
    }
  };

  return (
    <ImageBackground
      source={require('./assets/backgroundGame.png')}
      style={styles.backgroundImage}
      resizeMode="cover">
      <SafeAreaView edges={['top']} />

      <View style={styles.container}>
        {/* Header */}
        <HeadGame
          gameId={ConfigAPI.gameSKXT}
          isShowSuggest={true}
          onUseHint={() => setShowModelConfirm(true)}
          timeOut={() => gameOver('Hết giờ rồi, làm lại nào')}
        />
        <View>
          <LineProgressBar progress={(questionDone / totalQuestion) * 100} />
          <View style={styles.progressInfoContainer}>
            <Lives totalLives={maxLives} currentLives={currentLives} />
            <CountBadge current={questionDone} total={totalQuestion} />
          </View>
        </View>
        {/* Loading State */}
        {(loading || configLoading) && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#112164" />
            <Text style={styles.loadingText}>Đang tải câu hỏi...</Text>
          </View>
        )}

        {/* Error State */}
        {(error || configError) && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              {error || configError || 'Không có câu hỏi cho game này'}
            </Text>
          </View>
        )}

        {/* Game Content */}
        {initialized &&
        configInitialized &&
        currentQuestion &&
        !error &&
        !configError ? (
          <View style={styles.gameContent}>
            <View style={styles.questionContainer}>
              {currentQuestion.audioUrl ? (
                <TouchableOpacity
                  style={styles.audioButton}
                  onPress={playAudio}>
                  <Text style={styles.audioIcon}>
                    {isPlayingAudio ? '⏸️' : '🔊'}
                  </Text>
                </TouchableOpacity>
              ) : null}
              <Text style={styles.questionText}>
                {currentQuestion.questionText}
              </Text>
            </View>
            <GestureHandlerRootView style={{flex: 1}}>
              <View style={styles.container}>
                <View style={styles.mainContent}>
                  <Animated.View style={[styles.plateContainer]}>
                    <ImageBackground
                      source={require('./assets/plate.png')}
                      style={styles.plateBackground}
                      resizeMode="stretch">
                      <View style={styles.row}>
                        {sentence.map((word, index) => {
                          const wordId = `${word}-${index}`;
                          return (
                            <View key={`slot-${index}`} style={styles.slotWrap}>
                              <InsertZone
                                ref={ref => {
                                  if (ref) {
                                    insertZoneRefs.current[index] = ref;
                                  }
                                }}
                                index={index}
                                onReceiveDragDrop={word =>
                                  handleInsert(index, word)
                                }
                                isActive={activeZoneIndex === index}
                              />
                              <DraggableWordInSentence
                                word={word}
                                index={index}
                                isSubmitted={isAnswerCorrect ?? false}
                                isError={isAnswerCorrect === false}
                                isInserting={isInserting}
                                insertIndex={insertingAtIndex}
                                onDragStart={handleSentenceWordDragStart}
                                onDragMove={handleDragMove}
                                onDragEnd={handleSentenceWordDragEnd}
                                isDragging={currentlyDraggingWord === word}
                                dragId={wordId}
                              />
                            </View>
                          );
                        })}
                        <InsertZone
                          ref={ref => {
                            if (ref) {
                              insertZoneRefs.current[sentence.length] = ref;
                            }
                          }}
                          index={sentence.length}
                          onReceiveDragDrop={word =>
                            handleInsert(sentence.length, word)
                          }
                          isActive={activeZoneIndex === sentence.length}
                          isEndZone={true}
                        />
                      </View>
                      {/* Feedback Messages */}
                      {!feedbackMessage ? null : (
                        <View style={styles.feedbackContainer}>
                          <Text
                            style={[
                              styles.feedbackText,
                              feedbackType === 'success'
                                ? styles.successText
                                : styles.errorText,
                            ]}>
                            {feedbackMessage}
                          </Text>
                        </View>
                      )}
                    </ImageBackground>
                  </Animated.View>
                  <View style={styles.buttonContainer}>
                    <TouchableOpacity
                      style={[styles.button, styles.submitButton]}
                      onPress={handleSubmit}
                      disabled={isAnswerCorrect !== null}>
                      <Text style={styles.buttonText}>Kiểm tra đáp án</Text>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.wordBank}>
                    {draggableWords.map((word, index) => (
                      <DraggableWord
                        key={`drag-${index}-${word}`}
                        word={word}
                        // used={usedWords.has(word)}
                        onDragEnd={handleDragEnd}
                        onDragStart={handleDragStart}
                        onDragMove={handleDragMove}
                      />
                    ))}
                  </View>
                </View>
              </View>
            </GestureHandlerRootView>
          </View>
        ) : null}
        <BottomGame
          resetGame={() => {
            sakuXTHook.setData({stateName: 'wordsInDropZone', value: []});
            sakuXTHook.setData({stateName: 'wordsOutSideZone', value: []});
            // Reset game state
            setIsSubmitted(false);
            setUsedWords(new Set());
            // Reset animation states
            setIsInserting(false);
            setInsertingAtIndex(-1);
            setActiveZoneIndex(null);
            setDraggingWordFromSentence(null);
            setCurrentlyDraggingWord(null);
            resetDragStates();

            // Reset sentence container height
            sentenceContainerHeight.setValue(SENTENCE_CONTAINER_HEIGHT.NORMAL);
            sakuXTHook.clearFeedback();
            sakuXTHook.reset();
            gameHook.restartGame();
            updateSentenceAndDraggableWords();
            if (configInitialized && timeLimit) {
              console.log(
                `[StartSakuXT] Syncing global timer with SakuXT config: ${timeLimit}s`,
              );
              gameHook.setData({stateName: 'time', value: timeLimit});
              gameHook.setData({stateName: 'isRunTime', value: true});

              // Sync lives với SakuXT config
              if (maxLives) {
                gameHook.setData({stateName: 'totalLives', value: maxLives});
                gameHook.setData({stateName: 'currentLives', value: maxLives});
              }
            }
            gameOver('Thất bại rồi, làm lại nào');
          }}
          backGame={() => {
            navigation.goBack();
          }}
          pauseGame={() => {
            togglePauseGame();
          }}
          volumeGame={() => {}}
        />
        <View style={styles.modalContainer}>
          <ModelConfirm
            isShow={showModelConfirm}
            closeModal={() => setShowModelConfirm(false)}
            onConfirm={useHint}
            message={`Bạn sẽ bị trừ 5 điểm khi sử dụng trợ giúp này`}
          />
          <HintModel
            isShow={showHintModel}
            closeModal={() => setShowHintModel(false)}
            text={currentQuestion?.Suggest || ''}
          />
          <GameOverModal
            visible={isGameOver}
            restartGame={() => {
              restartGame();
            }}
            onClose={() => {
              // if (currentLives === 0) {
              //   navigation.goBack();
              // }
              restartGame();
            }}
            message={messageGameOver}
            isTimeOut={false}
          />
          <WinnerModal
            visible={showWinnerModal}
            onClose={() => setShowWinnerModal(false)}
            restartGame={restartGame}
            currentLives={currentLives}
            competenceId={competenceId}
            gameId={ConfigAPI.gameSKXT}
          />
          <GamePauseModal
            visible={isPauseGame}
            message={'Bạn đang tạm dừng trò chơi'}
            onContinue={() => {
              togglePauseGame();
            }}
          />
        </View>
      </View>
      <SafeAreaView edges={['bottom']} />
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
    paddingVertical: 16,
  },
  mainContent: {
    flex: 1,
    width: '100%',
  },
  text: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
  questionContainer: {
    width: Dimensions.get('window').width - 32,
    minHeight: 65,
    marginBottom: 20,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    backgroundColor: '#FCF8E8',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
    paddingHorizontal: 8,
  },
  plateContainer: {
    width: Dimensions.get('window').width - 32,
    height: 200,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 5,
  },
  plateBackground: {
    width: Dimensions.get('window').width - 32,
    minHeight: 220,
    paddingHorizontal: 20,
    paddingTop: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  slotWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    flexShrink: 0,
  },
  wordBank: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
    padding: 10,
    width: '100%',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingVertical: 16,
    paddingHorizontal: 16,
    width: '100%',
  },
  button: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minWidth: 40,
  },
  submitButton: {
    backgroundColor: '#AE2B26',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  gameContent: {
    marginTop: 16,
    flex: 1,
    alignItems: 'center',
  },
  progressInfoContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalContainer: {
    zIndex: 1000,
  },
  errorText: {
    color: '#F44336',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
  feedbackText: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  successText: {
    color: '#4CAF50',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#112164',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
    paddingHorizontal: 20,
  },
  questionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
    textAlign: 'center',
  },
  audioButton: {
    marginRight: 10,
    padding: 6,
    borderRadius: 18,
    width: 36,
    height: 36,
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  audioIcon: {
    fontSize: 18,
  },
  feedbackContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 4,
    alignItems: 'center',
  },
});

export default SakuXayTo;
