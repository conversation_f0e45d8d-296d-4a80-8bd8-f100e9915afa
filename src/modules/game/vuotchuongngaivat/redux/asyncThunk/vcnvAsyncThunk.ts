import {createAsyncThunk} from '@reduxjs/toolkit';
import {VCNVDA} from '../../da/vcnvDA';

// Async thunks for API calls
const loadGameConfig = createAsyncThunk(
  'vcnv/loadGameConfig',
  async (gameId: string, {rejectWithValue}) => {
    try {
      const config = await VCNVDA.getGameConfig(gameId);
      return config;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to load game config');
    }
  },
);

const loadGameData = createAsyncThunk(
  'vcnv/loadGameData',
  async (
    params: {gameId: string; milestoneId: number; competenceId: string},
    {rejectWithValue},
  ) => {
    try {
      const vcnvDA = new VCNVDA();
      const crosswordData = await vcnvDA.loadCompleteGameData(
        params.gameId,
        params.milestoneId,
        params.competenceId,
      );
      return crosswordData;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to load game data');
    }
  },
);

export {loadGameConfig, loadGameData};
