import {createSlice} from '@reduxjs/toolkit';
import {question} from '../../data/question';
import {
  MiniQuestion,
  Question,
  VCNVCrosswordData,
  convertCrosswordDataToLegacyFormat,
} from '../../models/models';
import {VCNVGameConfig} from '../../types/vcnvTypes';
import {loadGameConfig, loadGameData} from '../asyncThunk/vcnvAsyncThunk';

interface State {
  // Legacy format for backward compatibility
  currentQuestion: Question | null;
  currentMiniQuestion: MiniQuestion | null;
  listMiniQuestion: MiniQuestion[] | null;
  usedHints: string[];
  // New API data
  crosswordData: VCNVCrosswordData | null;
  gameConfig: VCNVGameConfig | null;

  // Loading states
  loading: boolean;
  configLoading: boolean;
  error: string | null;
  configError: string | null;
  initialized: boolean;
  configInitialized: boolean;

  // Game parameters
  gameId: string;
  stage: number;
  competenceId: string;
}

const initialState: State = {
  // Legacy
  currentQuestion: null,
  currentMiniQuestion: null,
  listMiniQuestion: null,

  // New API data
  crosswordData: null,
  gameConfig: null,

  // Loading states
  loading: false,
  configLoading: false,
  error: null,
  configError: null,
  initialized: false,
  configInitialized: false,
  usedHints: [],
  // Game parameters
  gameId: '',
  stage: 1,
  competenceId: '',
};

export const GameSlice = createSlice({
  name: 'Game',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    setGameParams(state, action) {
      state.gameId = action.payload.gameId;
      state.stage = action.payload.stage;
      state.competenceId = action.payload.competenceId;
    },
    startGame: (state: State) => {
      // Legacy fallback
      state.currentQuestion = question;
      state.listMiniQuestion = question.miniQuestions;
      state.usedHints = [];
    },
    startGameWithAPI: (state: State) => {
      if (state.crosswordData) {
        // Convert new format to legacy format for backward compatibility
        const legacyQuestion = convertCrosswordDataToLegacyFormat(
          state.crosswordData,
        );
        state.currentQuestion = legacyQuestion;
        state.listMiniQuestion = legacyQuestion.miniQuestions;
      }
    },
    resetGame: (state: State) => {
      state.currentQuestion = null;
      state.currentMiniQuestion = null;
      state.listMiniQuestion = null;
      state.crosswordData = null;
      state.error = null;
      state.initialized = false;
      state.usedHints = [];
    },
    markHintUsed(state, action) {
      const questionId = action.payload;
      if (!state.usedHints.includes(questionId)) {
        state.usedHints.push(questionId);
      }
    },
  },
  extraReducers: builder => {
    // Load game config
    builder
      .addCase(loadGameConfig.pending, state => {
        state.configLoading = true;
        state.configError = null;
      })
      .addCase(loadGameConfig.fulfilled, (state, action) => {
        state.configLoading = false;
        state.gameConfig = action.payload;
        state.configInitialized = true;
      })
      .addCase(loadGameConfig.rejected, (state, action) => {
        state.configLoading = false;
        state.configError = action.payload as string;
        state.configInitialized = false;
      });

    // Load game data
    builder
      .addCase(loadGameData.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadGameData.fulfilled, (state, action) => {
        state.loading = false;
        state.crosswordData = action.payload;
        state.initialized = true;

        // Auto convert to legacy format
        const legacyQuestion = convertCrosswordDataToLegacyFormat(
          action.payload,
        );
        state.currentQuestion = legacyQuestion;
        state.listMiniQuestion = legacyQuestion.miniQuestions;
      })
      .addCase(loadGameData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.initialized = false;
      });
  },
});

export const {
  setData,
  setGameParams,
  startGame,
  startGameWithAPI,
  resetGame,
  markHintUsed,
} = GameSlice.actions;

export default GameSlice.reducer;
