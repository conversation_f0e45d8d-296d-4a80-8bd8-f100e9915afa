import { DataController } from '../../../../base/baseController';
import { shuffleArray } from '../../../../utils/arrayUtils';
import {
  SakuSMGameConfigAPI,
  SakuSMGameQuestionAPI,
  SakuSMGameAnswerAPI,
  SakuSMGameConfig,
  SakuSMQuestion,
  SakuSMAnswer,
  ApiResponse,
} from '../types/sakuSMTypes';

export class SakuSMDA {
  private questionController: DataController;
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  /**
   * L<PERSON><PERSON> c<PERSON>u hình game từ bảng GameConfig
   * @param gameId ID của game SakuSM
   * @returns Promise<SakuSMGameConfig>
   */
  static async getGameConfig(gameId: string): Promise<SakuSMGameConfig> {
    try {
      console.log(`[SakuSMDA] Loading game config for GameId: ${gameId}`);

      const controller = new DataController('GameConfig');
      const response: ApiResponse<SakuSMGameConfigAPI> = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });

      if (response.code !== 200 || !response.data || response.data.length === 0) {
        throw new Error('No game config found or API returned unsuccessful response');
      }

      const configData = response.data[0];
      const transformedConfig: SakuSMGameConfig = {
        gameId: gameId,
        scorePerLife: configData.Score || 10,
        maxLives: configData.LifeCount || 3,
        timeLimit: configData.Time || 300,
        bonusScore: configData.Bonus || 50,
        isActive: configData.IsActive || true,
        gemHint: configData.ScoreHint || 10,

      };

      console.log('[SakuSMDA] Successfully loaded game config:', transformedConfig);
      return transformedConfig;

    } catch (error) {
      console.error('[SakuSMDA] Error loading game config:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuestion và GameAnswer
   * @param gameId ID của game SakuSM
   * @param stage Stage hiện tại
   * @param competenceId ID của competence
   * @returns Promise<SakuSMQuestion[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number,
    competenceId: string,
  ): Promise<SakuSMQuestion[]> {
    try {
      console.log(`[SakuSMDA] Loading questions for GameId: ${gameId}, Stage: ${stage}, CompetenceId: ${competenceId}`);
      
      // Lấy danh sách câu hỏi
      const questionResponse: ApiResponse<SakuSMGameQuestionAPI> =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });

      if (questionResponse.code !== 200) {
        throw new Error(`API returned error code: ${questionResponse.code}`);
      }

      if (!questionResponse.data || questionResponse.data.length === 0) {
        console.warn('[SakuSMDA] No questions found for the given criteria');
        return [];
      }

      // Lấy tất cả đáp án cho các câu hỏi này
      const questionIds = questionResponse.data.map(q => q.Id);
      const answerResponse: ApiResponse<SakuSMGameAnswerAPI> =
        await this.answerController.getListSimple({
          query: `@GameQuestionId: {${questionIds.join('|')}}`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });

      if (answerResponse.code !== 200) {
        throw new Error(`Answer API returned error code: ${answerResponse.code}`);
      }

      // Transform data thành format game cần
      const transformedQuestions: SakuSMQuestion[] = questionResponse.data.map(question => {
        // Tìm tất cả đáp án cho câu hỏi này
        const questionAnswers = answerResponse.data.filter(ans => ans.GameQuestionId === question.Id);
        
        if (questionAnswers.length === 0) {
          console.warn(`[SakuSMDA] No answers found for question ${question.Id}`);
          return null;
        }

        // Transform answers và random thứ tự
        const transformedAnswers: SakuSMAnswer[] = shuffleArray(
          questionAnswers.map(answer => ({
            id: answer.Id,
            text: answer.Name || '',
            isTrue: answer.IsResult || false,
          }))
        );

        return {
          id: question.Id,
          question: question.Name || '',
          hint: question.Suggest || '', // Sử dụng field Suggest làm hint
          answers: transformedAnswers,
        };
      }).filter(q => q !== null) as SakuSMQuestion[];

      // Random thứ tự câu hỏi
      const shuffledQuestions = shuffleArray(transformedQuestions);

      console.log(`[SakuSMDA] Successfully loaded ${shuffledQuestions.length} questions`);
      return shuffledQuestions;

    } catch (error) {
      console.error('[SakuSMDA] Error loading questions:', error);
      throw error;
    }
  }
}
