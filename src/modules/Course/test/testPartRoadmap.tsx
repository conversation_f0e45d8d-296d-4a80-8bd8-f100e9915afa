import React, { useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import { useProccessPartLesson } from '../../../redux/hook/proccessLessonHook';

interface TestPartRoadmapProps {
  courseId: string;
}

const TestPartRoadmap: React.FC<TestPartRoadmapProps> = ({ courseId }) => {
  const { lessons, loading, error } = useProccessPartLesson(courseId);

  useEffect(() => {
    console.log('=== TEST PART ROADMAP ===');
    console.log('Course ID:', courseId);
    console.log('Loading:', loading);
    console.log('Error:', error);
    console.log('Lessons data:', JSON.stringify(lessons, null, 2));
  }, [lessons, loading, error, courseId]);

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Testing Part Roadmap</Text>
        <Text>Loading...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Testing Part Roadmap</Text>
        <Text style={styles.error}>Error: {error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Testing Part Roadmap</Text>
      <Text style={styles.subtitle}>Course ID: {courseId}</Text>
      <Text style={styles.subtitle}>Total Steps: {lessons?.length || 0}</Text>
      
      {lessons?.map((lesson: any, index: number) => (
        <View key={lesson.id} style={styles.lessonContainer}>
          <Text style={styles.lessonTitle}>
            {index + 1}. {lesson.title}
          </Text>
          
          {lesson.partName && (
            <Text style={styles.partName}>
              📚 Part: {lesson.partName}
            </Text>
          )}
          
          {lesson.isFirstStepOfPart && (
            <Text style={styles.firstStep}>
              🎯 First step of part
            </Text>
          )}
          
          {lesson.steps?.map((step: any, stepIndex: number) => (
            <View key={stepIndex} style={styles.stepContainer}>
              <Text style={styles.stepText}>
                Step {step.order}: {step.displayName} ({step.type})
              </Text>
              <Text style={styles.progressText}>
                Progress: {step.PercentCompleted}%
              </Text>
              {step.partId && (
                <Text style={styles.metaText}>
                  Part ID: {step.partId}
                </Text>
              )}
            </View>
          ))}
        </View>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 8,
    color: '#666',
  },
  lessonContainer: {
    backgroundColor: 'white',
    padding: 12,
    marginBottom: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  lessonTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  partName: {
    fontSize: 14,
    color: '#007bff',
    marginBottom: 4,
  },
  firstStep: {
    fontSize: 12,
    color: '#28a745',
    marginBottom: 8,
  },
  stepContainer: {
    backgroundColor: '#f8f9fa',
    padding: 8,
    marginTop: 8,
    borderRadius: 4,
  },
  stepText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#007bff',
    marginBottom: 2,
  },
  metaText: {
    fontSize: 10,
    color: '#999',
  },
  error: {
    color: 'red',
    fontSize: 14,
  },
});

export default TestPartRoadmap;
