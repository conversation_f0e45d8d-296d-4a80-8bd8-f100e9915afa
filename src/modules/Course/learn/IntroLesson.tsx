import {Dimensions, Image, ScrollView, StyleSheet, Text, View} from 'react-native';
import {
  AppButton,
  FBottomSheet,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {useEffect, useRef, useState} from 'react';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import {SafeAreaView} from 'react-native-safe-area-context';
import VideoPlayerWithFullscreen from '../components/VideoPlayerWithFullscreen';
import {BaseDA} from '../../../base/BaseDA';
import {useTranslation} from 'react-i18next';
import RenderHTML from 'react-native-render-html';
// import VideoPlayer, {type VideoPlayerRef} from 'react-native-video-player';

export default function IntroductionLesson(pros: any) {
  const {t} = useTranslation();
  const [video, setVideo] = useState<any>();
  useEffect(() => {
    const getVideo = async () => {
      if(!pros.lessonData?.Introduction || pros.lessonData?.Introduction.includes('http')) return;
      const rs = await BaseDA.getFilesInfor([pros.lessonData?.Introduction]);
      if (rs) {
        setVideo(rs.data[0]);
      }
    };
    getVideo();
  }, [pros.lessonData]);
  const bottomSheetRef = useRef<any>(null);
  return (
    <ScrollView style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <SafeAreaView edges={['top']} />
        <View style={styles.videoContainer}>
          {video?.Url ? (
            <VideoPlayerWithFullscreen
              key={video?.Url}
              source={pros.lessonData?.Introduction.includes('http') ? pros.lessonData?.Introduction : ConfigAPI.url.replace('/api/', '') + video?.Url}
              // onProgressPercent={handleVideoProgress}
              onLoad={time => {
                // Lưu thời lượng video
                console.log('Video loaded, duration:', time);
                // setTime(time);
              }}
            />
          ) : (
            <View style={styles.placeholderContainer}>
              <Image
                style={styles.placeholderImage}
                source={require('../../../assets/appstore.png')}
              />
              <Text style={{...TypoSkin.title3}}>{t('course.noVideo')}</Text>
            </View>
          )}
        </View>
      {pros.lessonData && (
        <View style={styles.section}>
          {pros.lessonData.Introduction && (
            <View style={styles.introSection}>
              <RenderHTML
                contentWidth={Dimensions.get('window').width - 8}
                source={{html: pros.lessonData.Content}}
                tagsStyles={{
                  body: {
                    color: '#313135',
                    fontSize: 14,
                    lineHeight: 20,
                    fontFamily: 'Inter',
                  },
                  div: {
                    color: '#313135',
                    fontSize: 14,
                    lineHeight: 20,
                    fontFamily: 'Inter',
                  },
                  p: {
                    color: '#313135',
                    fontSize: 14,
                    lineHeight: 20,
                    fontFamily: 'Inter',
                  },
                  h1: {
                    fontSize: 24,
                    fontWeight: 'bold',
                    marginBottom: 16,
                    color: '#18181B',
                    fontFamily: 'Inter',
                  },
                  h2: {
                    fontSize: 20,
                    fontWeight: 'bold',
                    marginBottom: 14,
                    color: '#18181B',
                    fontFamily: 'Inter',
                  },
                  h3: {
                    fontSize: 18,
                    fontWeight: 'bold',
                    marginBottom: 12,
                    color: '#18181B',
                    fontFamily: 'Inter',
                  },
                  ul: {
                    marginLeft: 20,
                    marginBottom: 10,
                  },
                  li: {
                    color: '#313135',
                    fontSize: 14,
                    lineHeight: 20,
                    marginBottom: 5,
                    fontFamily: 'Inter',
                  },
                }}
              />
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    zIndex: 1,
    position: 'absolute',
  },
  loadingContainer: {
    height: 200,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoContainer: {
    height: 'auto',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  tabIndicator: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    height: 1.5,
  },
  tabStyle: {
    paddingHorizontal: 4,
    paddingTop: 0,
  },
  tabBar: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    height: 45,
    elevation: 0,
  },
  placeholderContainer: {
    height: 200,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },

  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
  section: {
    marginBottom: 24,
    padding: 16,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 12,
  },
  lessonInfo: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    overflow: 'hidden',
  },
  courseInfo: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    overflow: 'hidden',
  },
  listTile: {
    padding: 12,
    gap: 16, // Add gap between elements
  },
  leadingContainer: {
    // marginRight: 12, // Add extra space between icon and text
  },
  introSection: {
    marginTop: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  introTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 8,
  },
  introContent: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
});
