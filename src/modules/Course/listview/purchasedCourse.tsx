import {Dimensions, FlatList, RefreshControl, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {FBottomSheet, ProgressSlider} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {useEffect, useRef, useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {RootScreen} from '../../../router/router';
import {DefaultProduct} from '../../Default/card/defaultProduct';
import {CourseDA} from '../da';
import {ActivityIndicator} from 'react-native-paper';
import EmptyPage from '../../../Screen/emptyPage';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

export default function PurchasedCourse() {
  const bottomSheetRef = useRef<any>(null);

  const [data, setData] = useState<Array<any>>([]);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const courseDA = new CourseDA();
  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    setLoading(true);
    setData([]);
    const result = await courseDA.getMyCourse();
    if (result) {
      setData(result);
    }
    setLoading(false);
    setRefresh(false);
  };
  const onRefresh = async () => {
    setRefresh(true);
    await getData();
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      {/* courses */}
      <FlatList
        nestedScrollEnabled
        // scrollEnabled={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        data={data}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultProduct
              key={index}
              flexDirection="row"
              containerStyle={{
                paddingVertical: 8,
                paddingHorizontal: 16,
                width: Dimensions.get('screen').width - 32,
                borderRadius: 8,
                marginHorizontal: 16,
                borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                borderWidth: 1,
              }}
              noDivider
              onPressDetail={() => {
                // navigation.push(RootScreen.Test,{id: '1fb7661dec514c18a11f3b53d8add27b'});
                navigation.push(RootScreen.ProccessCourse, {
                  id: item.CourseId,
                  courseCustomerId: item.Id,
                  name: item.Name,
                  isCertificate: item.IsCertificate,
                  author: item.Author,
                  signature: item.Signature,
                  certificateName: item.CertificateName,
                });
              }}
              titleStyle={{
                ...TypoSkin.heading7,
                fontWeight: '500',
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                paddingRight: 32,
              }}
              data={item}
              reportContent={
                <View
                  style={{
                    paddingTop: 12,
                    paddingRight: 16,
                  }}>
                  <ProgressSlider
                    style={{
                      width: Dimensions.get('screen').width - 32 - 32 - 16 - 64,
                    }}
                    height={6}
                    progress={(item.Process ?? 0) / (item.TotalLesson ?? 0)}
                    backgroundColor={
                      ColorThemes.light.Neutral_Background_Color_Main
                    }
                    progressColor={ColorThemes.light.Primary_Color_Main}
                  />
                </View>
              }
            />
          );
        }}
        style={{width: '100%', height: '100%', paddingTop: 32}}
        keyExtractor={a => a.Id?.toString()}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() => <View style={{height: 100}} />}
        ListEmptyComponent={() => {
          if (isLoading) {
            return [1, 2, 3].map((_, index) => (
              <SkeletonPlaceCourse key={`skeleton-${index}`} />
            ));
          }
          if (data.length === 0 && !isLoading) {
            return <EmptyPage title={t('nodata')} />;
          }
        }}
      />
    </View>
  );
}

function SkeletonPlaceCourse() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{paddingVertical: 8, paddingHorizontal: 16}}>
        <View
          style={{
            flexDirection: 'row',
            marginHorizontal: 16,
            marginBottom: 16,
            padding: 16,
            borderRadius: 8,
            borderWidth: 1,
            borderColor: '#e0e0e0',
            gap: 12,
          }}>
          {/* Course image placeholder */}
          <View
            style={{
              width: 80,
              height: 80,
              borderRadius: 8,
            }}
          />

          {/* Course details */}
          <View style={{flex: 1, gap: 8}}>
            {/* Title placeholder */}
            <View
              style={{
                width: '80%',
                height: 16,
                borderRadius: 4,
              }}
            />

            {/* Subtitle placeholder */}
            <View
              style={{
                width: '60%',
                height: 12,
                borderRadius: 4,
              }}
            />

            {/* Progress bar placeholder */}
            <View
              style={{
                width: '100%',
                height: 6,
                borderRadius: 3,
                marginTop: 8,
              }}
            />
          </View>
        </View>
      </View>
    </SkeletonPlaceholder>
  );
}
