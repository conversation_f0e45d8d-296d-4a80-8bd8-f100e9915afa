/* eslint-disable react-native/no-inline-styles */
import React, {useMemo} from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {AppButton, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import ConfigAPI from '../../../Config/ConfigAPI';
import {Ultis} from '../../../utils/Utils';

interface Props {
  containerStyle?: ViewStyle;
  flexDirection?: 'default' | 'row';
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  priceStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: any;
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
  onPressLikeAction?: () => void;
  onPressDetail?: () => void;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  horizontalList?: boolean;
  noDivider?: boolean;
  dividerColor?: string;
}

export function DefaultProduct(props: Props) {
  const isLike = useMemo(() => {
    return props.data.IsLike ?? false;
  }, [props.data.IsLike]);

  return (
    <TouchableOpacity
      onPress={props.onPressDetail}
      style={[
        {
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          alignSelf: 'flex-start',
          flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          width: Dimensions.get('screen').width - 48,
          flex: 1,
          ...props.containerStyle,
        },
      ]}>
      <View
        style={[
          stylesDefault.mainContainer,
          {
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          },
        ]}>
        <View
          style={[
            stylesDefault.img,
            {
              height: props.flexDirection === 'row' ? 64 : 234,
              alignSelf: 'flex-start',
              width:
                props.flexDirection === 'row'
                  ? 64
                  : Dimensions.get('screen').width - 48,
              borderColor: ColorThemes.light.Neutral_Border_Color_Main,
              borderWidth: 1,
              ...props.imgStyle,
            },
          ]}>
          <Image
            key={props.data.Img}
            source={{
              uri: props?.data.Img
                ? `${ConfigAPI.urlImg + props?.data.Img}`
                : 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
            }}
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 8,
              objectFit: 'cover',
              position: 'absolute',
            }}
            resizeMode="cover"
          />
          {props.flexDirection === 'default' ? (
            <AppButton
              backgroundColor={'transparent'}
              borderColor="transparent"
              onPress={() => {
                if (props.onPressLikeAction) {
                  props.onPressLikeAction();
                }
              }}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 24,
                width: 24,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Main,
                zIndex: 1,
                position: 'absolute',
                right: 16,
                top: 16,
              }}
              title={
                <Winicon
                  src={
                    isLike ? 'fill/emoticons/heart' : 'outline/emoticons/heart'
                  }
                  size={15}
                  color={
                    isLike
                      ? ColorThemes.light.Error_Color_Main
                      : ColorThemes.light.Neutral_Text_Color_Title
                  }
                />
              }
            />
          ) : null}
        </View>
        <View
          style={{
            borderBottomColor:
              props.dividerColor ?? ColorThemes.light.Neutral_Border_Color_Main,
            borderBottomWidth: props.noDivider ? 0 : 1,
            paddingBottom: 8,
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
            alignSelf: 'flex-start',
          }}>
          <View style={[stylesDefault.mainContent]}>
            {/* infor on top */}
            {props.data.relativeUser ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  gap: 4,
                  marginBottom: 4,
                }}>
                {/* infor img */}
                <View
                  style={[
                    {
                      width: 20,
                      height: 20,
                      borderRadius: 100,
                      backgroundColor: 'black',
                    },
                  ]}>
                  <Image
                    source={{
                      uri: props?.data?.relativeUser?.image
                        ? `${
                            ConfigAPI.urlImg + props?.data?.relativeUser?.image
                          }`
                        : 'https://placehold.co/40/FFFFFF/000000/png',
                    }}
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                </View>
                {/* infor text */}
                <View style={{width: 'auto'}}>
                  <Text style={[stylesDefault.inforTitle]}>
                    {props.data?.relativeUser?.title ?? ''}
                  </Text>
                </View>
              </View>
            ) : null}
            {/* title */}
            {props.data.Name ? (
              <Text
                style={[
                  {
                    ...stylesDefault.titleStyle,
                    paddingBottom: props.data.Description ? 4 : 0,
                    maxWidth:
                      props.flexDirection === 'row'
                        ? Dimensions.get('screen').width -
                          (typeof props.imgStyle?.width === 'number'
                            ? props.imgStyle.width
                            : 64) -
                          48
                        : undefined,
                    ...props.titleStyle,
                  },
                ]}
                numberOfLines={1}>
                {props.data.Name ?? ''}
              </Text>
            ) : null}
            {/* subtitle */}
            {props.data.Description && props.flexDirection !== 'row' ? (
              <View style={{paddingTop: 4, paddingBottom: 6}}>
                <Text
                  style={[stylesDefault.subTitleStyle, props.subtitleStyle]}
                  numberOfLines={2}>
                  {props.data.Description ?? ''}
                </Text>
              </View>
            ) : null}
            {props.data.Price ? (
              <Text
                style={[
                  {...TypoSkin.heading7},
                  {
                    marginTop: 12,
                    ...props.priceStyle,
                  },
                ]}>
                {props.data.Price == 0
                  ? 'Miễn phí'
                  : Ultis.money(props.data.Price ?? 0) + ' đ'}
              </Text>
            ) : null}
            {props.listItems?.length ? (
              <View style={{width: '100%', paddingTop: 5}}>
                {props.listItems.map((item, index) => {
                  return (
                    <View
                      key={index}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingVertical: 4,
                        gap: 8,
                      }}>
                      <View
                        style={[
                          {
                            width: 20,
                            borderRadius: 100,
                          },
                        ]}>
                        {item.icon ? (
                          <Winicon src={item.icon} size={20} />
                        ) : (
                          <Text style={[stylesDefault.inforTitle]}>*</Text>
                        )}
                      </View>
                      <Text
                        style={[
                          {
                            ...TypoSkin.label4,
                            color:
                              ColorThemes.light.Neutral_Text_Color_Subtitle,
                          },
                        ]}>
                        {item.title}
                      </Text>
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.onPressSeeMore && props.listItems?.length ? (
              <AppButton
                title={'See more'}
                containerStyle={{
                  justifyContent: 'flex-start',
                  alignSelf: 'baseline',
                  marginVertical: 8,
                }}
                backgroundColor={'transparent'}
                textStyle={TypoSkin.buttonText3}
                borderColor="transparent"
                suffixIconSize={16}
                suffixIcon={'outline/arrows/circle-arrow-right'}
                onPress={props.onPressSeeMore}
                textColor={ColorThemes.light.Info_Color_Main}
              />
            ) : null}
            {props.listTags?.length ? (
              <View style={{width: '100%', flexDirection: 'row', gap: 8}}>
                {props.listTags.map((item, index) => {
                  return (
                    <View
                      key={index}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        backgroundColor:
                          ColorThemes.light.Neutral_Background_Color_Main,
                        paddingHorizontal: 8,
                        borderRadius: 24,
                        borderColor:
                          ColorThemes.light.Neutral_Border_Color_Bolder,
                        borderWidth: 1,
                        paddingVertical: 4,
                        gap: 4,
                      }}>
                      <Text style={[stylesDefault.inforTitle]}>
                        {item.title}
                      </Text>
                      <Winicon src={'outline/arrows/right-arrow'} size={12} />
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.reportContent ? (
              <View style={{}}>
                {props.reportContent ? props.reportContent : null}
              </View>
            ) : null}
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
              }}>
              {props.flexDirection === 'default' && props.actionView && (
                <View
                  style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                  {props.actionView ? props.actionView : null}
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      gap: 4,
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}>
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => {}}
                      containerStyle={{padding: 4}}
                      title={
                        <Winicon src="outline/layout/circle-plus" size={20} />
                      }
                    />
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => {}}
                      containerStyle={{padding: 4}}
                      title={
                        <Winicon
                          src="outline/user interface/i-edit"
                          size={20}
                        />
                      }
                    />
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => {}}
                      containerStyle={{padding: 4}}
                      title={
                        <Winicon
                          src="outline/development/refresh-02"
                          size={18}
                        />
                      }
                    />
                  </View>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      justifyContent: 'flex-end',
                      alignItems: 'center',
                      gap: 8,
                    }}>
                    <AppButton
                      backgroundColor={
                        ColorThemes.light.Neutral_Background_Color_Main
                      }
                      borderColor="transparent"
                      onPress={() => {}}
                      textStyle={TypoSkin.buttonText3}
                      textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      containerStyle={{
                        paddingHorizontal: 12,
                        height: 32,
                        borderRadius: 8,
                      }}
                      title={'Button'}
                    />
                    <AppButton
                      backgroundColor={ColorThemes.light.Primary_Color_Main}
                      borderColor="transparent"
                      onPress={() => {}}
                      textStyle={TypoSkin.buttonText3}
                      textColor={
                        ColorThemes.light.Neutral_Background_Color_Absolute
                      }
                      containerStyle={{
                        paddingHorizontal: 12,
                        height: 32,
                        borderRadius: 8,
                      }}
                      title={'Button'}
                    />
                  </View>
                </View>
              )}
            </View>
          </View>
          {/* flex direction : row and with action view */}
          {props.flexDirection == 'row' ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
                width:
                  Dimensions.get('screen').width -
                  (Dimensions.get('screen').width -
                    48 -
                    64 -
                    (typeof props.imgStyle?.width === 'number'
                      ? props.imgStyle.width
                      : 64)),
              }}>
              {props.actionView ? props.actionView : null}
              {props.onPressLikeAction ? (
                <AppButton
                  backgroundColor={'transparent'}
                  borderColor="transparent"
                  onPress={() => {
                    if (props.onPressLikeAction) {
                      props.onPressLikeAction();
                    }
                  }}
                  containerStyle={{
                    borderRadius: 100,
                    padding: 6,
                    height: 32,
                    width: 32,
                    backgroundColor:
                      ColorThemes.light.Neutral_Background_Color_Main,
                  }}
                  title={
                    <Winicon
                      src={
                        isLike
                          ? 'fill/emoticons/heart'
                          : 'outline/emoticons/heart'
                      }
                      size={18}
                      color={
                        isLike
                          ? ColorThemes.light.Error_Color_Main
                          : ColorThemes.light.Neutral_Text_Color_Title
                      }
                    />
                  }
                />
              ) : null}
            </View>
          ) : null}
        </View>
      </View>
    </TouchableOpacity>
  );
}

export function SkeletonPlaceCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{...stylesDefault.mainContainer, marginBottom: 16}}>
        <View style={stylesDefault.img} />
        <View style={{flex: 1}}>
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
              marginBottom: 6,
              backgroundColor: '#e0e0e0',
            }}
          />
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
              backgroundColor: '#e0e0e0',
            }}
          />
        </View>
      </View>
      <View style={stylesDefault.mainContent}>
        <View
          style={{
            width: '100%',
            height: 16,
            borderRadius: 4,
            marginBottom: 6,
            backgroundColor: '#e0e0e0',
          }}
        />
        <View
          style={{
            width: '80%',
            height: 16,
            borderRadius: 4,
            backgroundColor: '#e0e0e0',
          }}
        />
      </View>
    </SkeletonPlaceholder>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});
