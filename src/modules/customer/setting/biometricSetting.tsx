import {
  Text,
  View,
  Modal,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import {navigateBack} from '../../../router/router';
import {
  FDialog,
  ListTile,
  showSnackbar,
  ComponentStatus,
  Winicon,
} from 'wini-mobile-components';
import {useState, useEffect, useRef} from 'react';
import ReactNativeBiometrics from 'react-native-biometrics';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {FaceID, TouchID} from '../../../features/local-authen/local-authen';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ScreenHeader from '../../../Screen/Layout/header';
import {SafeAreaView} from 'react-native-safe-area-context';
import IOSSwitch from '../../../components/IOSSwitch';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';

export default function BiometricSetting() {
  const customer = useSelectorCustomerState().data;
  const [biometric, setBiometric] = useState(false);
  const [biometricType, setBiometricType] = useState<any>();
  const [avaiBiometric, setAvaiBiometric] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [password, setPassword] = useState('');
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [pendingValue, setPendingValue] = useState(false);
  const rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });
  const dialogRef = useRef<any>(null);
  useEffect(() => {
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        setAvaiBiometric(true);
        getDataToAsyncStorage('biometryType').then(result => {
          setBiometricType(result);
        });
        getDataToAsyncStorage('Biometrics').then(result => {
          setBiometric(result == 'true' ? true : false);
        });
      } else {
        setAvaiBiometric(false);
        setBiometric(false);
      }
    });
  }, []);

  const checkPassword = (vl: boolean) => {
    setPendingValue(vl);
    setShowPasswordModal(true);
  };

  const handlePasswordConfirm = async () => {
    if (!password.trim()) {
      showSnackbar({
        message: 'Vui lòng nhập mật khẩu',
        status: ComponentStatus.ERROR,
      });
      return;
    }

    try {
      const res = await CustomerActions.checkPassword(
        customer?.Mobile || '',
        password,
      );

      if (res.code === 200) {
        // Mật khẩu đúng, đóng modal và gọi biometric prompt
        setShowPasswordModal(false);
        saveDataToAsyncStorage('Password', password);
        handleBiometricPrompt(pendingValue);
        setPassword('');
      } else {
        // Mật khẩu sai
        showSnackbar({
          message: 'Mật khẩu không đúng',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
    }
  };

  const handleBiometricPrompt = (vl: boolean) => {
    rnBiometrics
      .simplePrompt({promptMessage: 'Xác nhận sinh trắc học'})
      .then(resultObject => {
        const {success} = resultObject;
        if (success) {
          if (vl) {
            setTimeout(() => {
              saveDataToAsyncStorage('Biometrics', 'true');
              setBiometric(true);
            }, 100);
            showSnackbar({
              message: 'Bật sinh trắc học thành công',
              status: ComponentStatus.SUCCSESS,
            });
            return;
          } else {
            setTimeout(() => {
              saveDataToAsyncStorage('Biometrics', 'false');
              setBiometric(false);
            }, 100);
          }
        } else {
          setTimeout(() => {
            saveDataToAsyncStorage('Biometrics', biometric ? 'true' : 'false');
            setBiometric(biometric);
          }, 100);
          console.log('user cancelled biometric prompt', biometric);
        }
      })
      .catch(() => {
        setTimeout(() => {
          saveDataToAsyncStorage('Biometrics', 'false');
          setBiometric(false);
        }, 100);
        console.log('biometrics failed', biometric);
      });
  };

  const handleCloseModal = () => {
    setShowPasswordModal(false);
    setPassword('');
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FDialog ref={dialogRef} />
      <ScreenHeader
        title="Cài đặt sinh trắc học"
        onBack={() => navigateBack()}
      />
      <View style={{paddingHorizontal: 16}}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.Neutral_Text_Color_Subtitle,
          }}>
          Sinh trắc học là một tính năng bảo mật cho phép bạn đăng nhập bằng vân
          tay hoặc khuôn mặt.
        </Text>
      </View>
      <ListTile
        leading={
          <View>
            {(() => {
              if (biometricType == 'TouchID') {
                return (
                  <TouchID
                    size={20}
                    color={
                      avaiBiometric
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Neutral_Text_Color_Disable
                    }
                  />
                );
              } else {
                return (
                  <FaceID
                    size={20}
                    color={
                      avaiBiometric
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Neutral_Text_Color_Disable
                    }
                  />
                );
              }
            })()}
          </View>
        }
        title="Sử dụng sinh trắc học"
        titleStyle={[
          TypoSkin.heading8,
          {
            color: avaiBiometric
              ? ColorThemes.light.Neutral_Text_Color_Title
              : ColorThemes.light.Neutral_Text_Color_Disable,
          },
        ]}
        trailing={
          <IOSSwitch
            value={biometric}
            disabled={!avaiBiometric}
            onColor={ColorThemes.light.Primary_Color_Main}
            onValueChange={checkPassword}
          />
        }
      />

      {/* Password Confirmation Modal */}
      <Modal
        visible={showPasswordModal}
        transparent={true}
        animationType="fade"
        onRequestClose={handleCloseModal}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            style={{
              flex: 1,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              justifyContent: 'center',
              alignItems: 'center',
              paddingHorizontal: 20,
            }}>
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <KeyboardAvoidingView
                style={{
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                  borderRadius: 12,
                  padding: 24,
                  width: '100%',
                  maxWidth: 400,
                }}>
                <Text
                  style={{
                    ...TypoSkin.heading6,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'center',
                    marginBottom: 16,
                  }}>
                  Xác nhận mật khẩu
                </Text>

                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    textAlign: 'center',
                    marginBottom: 24,
                  }}>
                  Vui lòng nhập mật khẩu để xác nhận thay đổi cài đặt sinh trắc
                  học
                </Text>

                <View
                  style={{
                    borderWidth: 1,
                    borderColor: ColorThemes.light.Neutral_Border_Color_Subtle,
                    borderRadius: 8,
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 24,
                  }}>
                  <TextInput
                    style={{
                      flex: 1,
                      height: 48,
                      paddingHorizontal: 16,
                      ...TypoSkin.body2,
                      color: ColorThemes.light.Neutral_Text_Color_Body,
                    }}
                    placeholder="Nhập mật khẩu của bạn"
                    placeholderTextColor={
                      ColorThemes.light.Neutral_Text_Color_Disable
                    }
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={isVisiblePass}
                    autoFocus={true}
                  />
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => setVisiblePass(!isVisiblePass)}>
                    <Winicon
                      src={
                        isVisiblePass
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                    />
                  </TouchableOpacity>
                </View>

                <View
                  style={{
                    flexDirection: 'row',
                    gap: 12,
                  }}>
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      height: 48,
                      backgroundColor:
                        ColorThemes.light.Neutral_Background_Color_Subtle,
                      borderRadius: 8,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onPress={handleCloseModal}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText3,
                        color: ColorThemes.light.Neutral_Text_Color_Body,
                      }}>
                      Hủy
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={{
                      flex: 1,
                      height: 48,
                      backgroundColor: ColorThemes.light.Primary_Color_Main,
                      borderRadius: 8,
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}
                    onPress={handlePasswordConfirm}>
                    <Text
                      style={{
                        ...TypoSkin.buttonText3,
                        color:
                          ColorThemes.light.Neutral_Background_Color_Absolute,
                      }}>
                      Xác nhận
                    </Text>
                  </TouchableOpacity>
                </View>
              </KeyboardAvoidingView>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
}
