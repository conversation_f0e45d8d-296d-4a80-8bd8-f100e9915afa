import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useEffect, useRef, useState} from 'react';
import {useDispatch} from 'react-redux';
import {ScreenFooter} from 'react-native-screens';
import {useForm} from 'react-hook-form';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {DataController} from '../../../base/baseController';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import {
  AppButton,
  ComponentStatus,
  FDialog,
  FLoading,
  showDialog,
  Winicon,
} from 'wini-mobile-components';
import ScreenHeader from '../../../Screen/Layout/header';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import WScreenFooter from '../../../Screen/Layout/footer';
import ConfigAPI from '../../../Config/ConfigAPI';
import {RootScreen} from '../../../router/router';
import EditCustomer from '../form/edit-customer';
import {SafeAreaView} from 'react-native-safe-area-context';
import {CustomerRankType} from '../../../Config/Contanst';
import {Ultis} from '../../../utils/Utils';
import {useTranslation} from 'react-i18next';

export default function SettingProfile() {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const userData = useSelectorCustomerState().data;
  const [onLoading, setOnLoading] = useState(false);
  const dispatch = useDispatch<any>();
  const methods = useForm({shouldFocusError: false});
  const [onEdit, setOnEdit] = useState(false);
  const [bankList, setBankList] = useState<Array<any>>([]);

  // useEffect(() => {
  //   const bankController = new DataController('Bank');
  //   bankController.getAll().then(res => {
  //     if (res.code === 200) setBankList(res.data);
  //   });
  // }, []);

  useEffect(() => {
    const data: any = userData ?? {};
    Object.keys(data).forEach(props => {
      if (data[props]) {
        if (props === 'Gender') {
          methods.setValue(props, data[props] ? 1 : 0);
        } else {
          methods.setValue(props, data[props]);
        }
      }
    });
  }, []);

  const submitEdit = async () => {
    const newData = methods.getValues();
    if (newData.gender) {
      newData.gender = newData.gender === 1;
    }
    dispatch(CustomerActions.edit(newData));
    setOnEdit(false);
  };

  const dialogDelAccRef = useRef<any>(null);
  const dialogDelAcc = () => {
    showDialog({
      ref: dialogDelAccRef,
      status: ComponentStatus.WARNING,
      title: t('profile.deleteAccountConfirm'),
      content: t('profile.deleteAccountWarning'),
      onSubmit: async () => {
        if (userData?.Id) {
          setOnLoading(true);
          await CustomerActions.delete(dispatch, userData.Id, navigation).then(
            () => {
              setOnLoading(false);
            },
          );
        }
      },
    });
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FLoading
        visible={onLoading}
        avt={require('../../../assets/appstore.png')}
      />
      <FDialog ref={dialogDelAccRef} />
      <ScreenHeader
        title={onEdit ? t('profile.editInfo') : t('profile.title')}
        onBack={() => navigation.goBack()}
        action={
          !onEdit ? (
            <TouchableOpacity
              style={{padding: 16}}
              onPress={() => {
                setOnEdit(true);
              }}>
              <Winicon
                src="outline/users/user-edit"
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                size={20}
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={{padding: 16}}
              onPress={() => {
                setOnEdit(false);
              }}>
              <Text
                style={{
                  ...TypoSkin.buttonText4,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {t('common.cancel')}
              </Text>
            </TouchableOpacity>
          )
        }
      />
      <ScrollView>
        {onEdit ? (
          <View style={{flex: 1}}>
            <EditCustomer methods={methods} bankList={bankList} />
          </View>
        ) : (
          <View style={{backgroundColor: '#fff'}}>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.name')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Name ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.email')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Email ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.phone')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Mobile ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.rank')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.RankInfor
                  ? `${t('profile.rankPrefix')} ${
                      userData?.RankInfor?.Name ?? ''
                    }`
                  : t('profile.normal')}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.birthdate')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {Ultis.datetoString(new Date(userData?.Dob ?? 0))}
              </Text>
            </View>

            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.gender')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Gender == 1
                  ? t('profile.male')
                  : t('profile.female')}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>{t('profile.address')}</Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {userData?.Address ?? '-'}
              </Text>
            </View>
            <View
              style={{
                paddingVertical: 12,
                paddingHorizontal: 16,
                flexDirection: 'row',
                gap: 12,
                alignItems: 'center',
              }}>
              <Text style={TypoSkin.regular2}>
                {t('profile.accountCreationDate')}
              </Text>
              <Text
                style={[
                  TypoSkin.regular2,
                  {
                    flex: 1,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                    textAlign: 'right',
                  },
                ]}>
                {Ultis.formatDateTime(userData?.DateCreated, false)}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
      {onEdit ? null : (
        <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
          <AppButton
            containerStyle={{
              borderRadius: 8,
              marginHorizontal: 16,
              marginBottom: 65,
              flex: 1,
              backgroundColor: ColorThemes.light.Info_Color_Main,
              justifyContent: 'center',
            }}
            borderColor="transparent"
            title={
              !userData?.Password
                ? t('profile.createPassword')
                : t('profile.updatePassword')
            }
            onPress={() => {
              navigation.push(RootScreen.ForgotPass);
            }}
          />
        </WScreenFooter>
      )}
      <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
        <AppButton
          containerStyle={{
            borderRadius: 8,
            marginHorizontal: 16,
            flex: 1,
            backgroundColor: onEdit
              ? ColorThemes.light.Primary_Color_Main
              : ColorThemes.light.Error_Color_Main,
            justifyContent: 'center',
          }}
          borderColor="transparent"
          title={onEdit ? t('profile.saveChanges') : t('profile.deleteAccount')}
          onPress={onEdit ? submitEdit : dialogDelAcc}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}
