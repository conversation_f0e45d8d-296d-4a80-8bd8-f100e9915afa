import {
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useForm} from 'react-hook-form';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import React, {useEffect, useRef, useState} from 'react';
import {
  showSnackbar,
  ComponentStatus,
  showPopup,
  FLoading,
  FDialog,
  FPopup,
  Winicon,
  AppButton,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {DataController} from '../../../base/baseController';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';
import WScreenFooter from '../../../Screen/Layout/footer';
import ScreenHeader from '../../../Screen/Layout/header';
import {regexPassWord} from '../../../utils/Utils';
import {validatePhoneNumber} from '../../../utils/validate';
import {TextFieldForm} from '../../Default/form/component-form';
import {SafeAreaView} from 'react-native-safe-area-context';
import {saveDataToAsyncStorage} from '../../../utils/AsyncStorage';

export default function ForgotPass() {
  const methods = useForm({shouldFocusError: false});
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const [isLoading, setLoading] = useState(false);
  const dialogRef = useRef<any>(null);
  const popupRef = useRef<any>(null);
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [isVisiblePass3, setVisiblePass3] = useState(true);
  const user = useSelectorCustomerState().data;
  const route = useRoute<any>();
  const customerController = new DataController('Customer');

  useEffect(() => {
    if (route?.params?.mobile) {
      methods.setValue('Mobile', route?.params?.mobile);
    }
  }, []);

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (isLoading) {
      timeoutVariable = setTimeout(() => setLoading(false), 20000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [isLoading]);

  const onSubmit = async () => {
    const {Mobile, OldPassword, NewPassword, ConfirmPassword} =
      methods.getValues();

    var mobile = user?.Mobile ?? Mobile?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile) && !mobile?.includes('@')) {
      mobile = '0' + mobile; // Add 0 at the beginning
      const val = validatePhoneNumber(mobile);
      if (!val || !mobile) {
        methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
        return;
      }
    }

    const searchRaw =
      user?.Email || mobile.includes('@')
        ? `@Email:("${user?.Email ?? mobile}")`
        : `@Mobile:("${user?.Mobile ?? mobile}")`;

    // check sdt da dang ky
    const resCustomers = await customerController.getListSimple({
      page: 1,
      size: 1,
      query: searchRaw,
    });

    if (resCustomers?.data?.length > 0) {
      if (user?.Password) {
        if (OldPassword === undefined || OldPassword?.length == 0) {
          methods.setError('OldPassword', {
            message: 'Mật khẩu không được để trống',
          });
          return;
        }
      }

      if (NewPassword === undefined || NewPassword.length == 0) {
        methods.setError('NewPassword', {
          message: 'Mật khẩu không được để trống',
        });
        return;
      }

      if (ConfirmPassword === undefined || ConfirmPassword.length == 0) {
        methods.setError('ConfirmPassword', {
          message: 'Mật khẩu không được để trống',
        });
        return;
      }
      if (user?.Password && OldPassword && OldPassword == NewPassword)
        return showSnackbar({
          message: 'Mật khẩu mới không được trùng mật khẩu cũ',
          status: ComponentStatus.ERROR,
        });
      if (NewPassword !== ConfirmPassword)
        return showSnackbar({
          message: 'Mật khẩu nhập lại không đúng',
          status: ComponentStatus.ERROR,
        });
      methods.clearErrors();

      const res =
        !user?.Password || route?.params?.isLogin
          ? {code: 200}
          : await CustomerActions.checkPassword(
              user?.Mobile ?? mobile,
              OldPassword,
            );
      switch (res.code) {
        case 403:
          methods.setError('OldPassword', {message: 'Mật khẩu không đúng'});
          break;
        case 200:
          setLoading(true);
          const hashPass = await CustomerActions.hashPassword(NewPassword);
          if (hashPass.code != 200) {
            setLoading(false);
            showSnackbar({
              message: hashPass.message,
              status: ComponentStatus.ERROR,
            });
            return;
          }
          await dispatch(
            CustomerActions.edit(
              user?.Mobile
                ? {...user, Password: hashPass.data}
                : {...resCustomers?.data[0], Password: hashPass.data},
            ),
          ).then(() => {
            setLoading(false);
            showSnackbar({
              message: 'Cập nhật mật khẩu thành công!',
              status: ComponentStatus.SUCCSESS,
            });
          });

          setLoading(false);
          navigation.goBack();
          break;
        default:
          break;
      }
    } else {
      showSnackbar({
        message: 'Tài khoản chưa được đăng ký',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
  };

  const onError = (data: any) => {
    console.log(data);
  };

  return (
    <SafeAreaView
      style={{
        width: '100%',
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FLoading
        visible={isLoading}
        avt={require('../../../assets/appstore.png')}
      />
      <FDialog ref={dialogRef} />
      <FPopup ref={popupRef} />
      <ScreenHeader
        title={
          route?.params?.isLogin
            ? 'Quên mật khẩu'
            : !user?.Password
            ? 'Tạo mật khẩu'
            : 'Cập nhật mật khẩu'
        }
        onBack={() => navigation.goBack()}
      />
      <KeyboardAvoidingView
        behavior={'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 45 : 0}
        style={{height: '100%'}}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{width: '100%', padding: 16}}>
          <View style={{width: '100%', gap: 16}}>
            {route?.params?.isLogin ? null : !user?.Password ? (
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Info_Color_Main,
                }}>
                Tài khoản của bạn chưa có mật khẩu, hãy tạo mật khẩu của bạn
              </Text>
            ) : (
              <TextFieldForm
                control={methods.control}
                name="OldPassword"
                secureTextEntry={isVisiblePass}
                label="Mật khẩu cũ"
                disabled={!user?.Password}
                suffix={
                  <TouchableOpacity
                    style={{padding: 12}}
                    onPress={() => {
                      setVisiblePass(!isVisiblePass);
                    }}>
                    <Winicon
                      src={
                        isVisiblePass
                          ? `outline/user interface/view`
                          : `outline/user interface/hide`
                      }
                      size={14}
                    />
                  </TouchableOpacity>
                }
                returnKeyType="done"
                placeholder={'Tạo mật khẩu của bạn'}
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                  paddingLeft: 16,
                  paddingVertical: 16,
                  marginBottom: methods.formState.errors.Password?.message
                    ? 16
                    : 8,
                }}
                register={methods.register}
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('OldPassword', {
                      message: 'Mật khẩu không được để trống',
                    });
                    return;
                  }
                  var pass = ev.trim();
                  if (!regexPassWord.test(pass))
                    return methods.setError('OldPassword', {
                      message: 'Mật khẩu sai định dạng, hãy thử lại',
                    });
                  methods.clearErrors('OldPassword');
                }}
              />
            )}
            {route?.params?.isLogin ? (
              <TextFieldForm
                control={methods.control}
                name="Mobile"
                placeholder="Nhập Email/số điện thoại của bạn"
                label="Email/Số điện thoại"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  backgroundColor: ColorThemes.light.transparent,
                  paddingHorizontal:
                    route?.params?.mobile?.includes('@') ||
                    !route?.params?.mobile
                      ? 16
                      : 0,
                  marginBottom: 8,
                }}
                register={methods.register}
                prefix={
                  route?.params?.mobile?.includes('@') ||
                  !route?.params?.mobile ? null : (
                    <View
                      style={{
                        flexDirection: 'row',
                        height: 46,
                        paddingHorizontal: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 8,
                        backgroundColor:
                          ColorThemes.light.Neutral_Background_Color_Main,
                        borderRadius: 8,
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.buttonText3,
                          color: ColorThemes.light.Neutral_Text_Color_Title,
                        }}>
                        +84
                      </Text>
                      <Winicon src="outline/arrows/down-arrow" size={16} />
                    </View>
                  )
                }
                type={
                  route?.params?.mobile?.includes('@')
                    ? 'email-address'
                    : 'number-pad'
                }
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('Mobile', {
                      message: 'Email/Số điện thoại không được để trống',
                    });
                    return;
                  }
                  if (
                    route?.params?.mobile?.includes('@') ||
                    !route?.params?.mobile
                  ) {
                    return;
                  }
                  var mobile = ev.trim();
                  // Check if the number doesn't already start with 0 or +84
                  if (!/^(\+84|0)/.test(mobile)) {
                    mobile = '0' + mobile; // Add 0 at the beginning
                  }
                  const val = validatePhoneNumber(mobile);
                  if (val) methods.clearErrors('Mobile');
                  else
                    methods.setError('Mobile', {
                      message: 'Số điện thoại không hợp lệ',
                    });
                }}
              />
            ) : null}
            <TextFieldForm
              control={methods.control}
              name="NewPassword"
              secureTextEntry={isVisiblePass2}
              suffix={
                <TouchableOpacity
                  style={{padding: 12}}
                  onPress={() => {
                    setVisiblePass2(!isVisiblePass2);
                  }}>
                  <Winicon
                    src={
                      isVisiblePass2
                        ? `outline/user interface/view`
                        : `outline/user interface/hide`
                    }
                    size={14}
                  />
                </TouchableOpacity>
              }
              label="Mật khẩu mới"
              returnKeyType="done"
              placeholder={'Tạo mật khẩu của bạn'}
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
                paddingLeft: 16,
                paddingVertical: 16,
                marginBottom: methods.formState.errors.NewPassword?.message
                  ? 16
                  : 8,
              }}
              register={methods.register}
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('NewPassword', {
                    message: 'Mật khẩu không được để trống',
                  });
                  return;
                }
                var pass = ev.trim();
                if (!regexPassWord.test(pass))
                  return methods.setError('NewPassword', {
                    message: 'Mật khẩu sai định dạng, hãy thử lại',
                  });
                methods.clearErrors('NewPassword');
              }}
            />
            <Text
              style={[
                TypoSkin.subtitle4,
                {
                  alignSelf: 'baseline',
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                },
              ]}>{`- Tối thiểu 8 ký tự/ Tối đa 16 ký tự \n- Gồm chữ hoa, thường và số`}</Text>
            <TextFieldForm
              control={methods.control}
              name="ConfirmPassword"
              label="Nhập lại mật khẩu mới"
              returnKeyType="done"
              secureTextEntry={isVisiblePass3}
              suffix={
                <TouchableOpacity
                  style={{padding: 12}}
                  onPress={() => {
                    setVisiblePass3(!isVisiblePass3);
                  }}>
                  <Winicon
                    src={
                      isVisiblePass3
                        ? `outline/user interface/view`
                        : `outline/user interface/hide`
                    }
                    size={14}
                  />
                </TouchableOpacity>
              }
              placeholder={'Nhập lại mật khẩu của bạn'}
              errors={methods.formState.errors}
              textFieldStyle={{
                height: 48,
                backgroundColor: ColorThemes.light.transparent,
                paddingLeft: 16,
                paddingVertical: 16,
              }}
              register={methods.register}
              onBlur={async (ev: string) => {
                if (ev === undefined || ev.length == 0) {
                  methods.setError('ConfirmPassword', {
                    message: 'Mật khẩu không được để trống',
                  });
                  return;
                }
                var rePass = ev.trim();
                if (methods.watch('NewPassword') !== rePass)
                  return methods.setError('ConfirmPassword', {
                    message: 'Mật khẩu nhập lại không đúng',
                  });
                methods.clearErrors('ConfirmPassword');
              }}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <WScreenFooter style={{flexDirection: 'row', alignItems: 'center'}}>
        <AppButton
          title={
            !user?.Password && !route?.params?.isLogin
              ? 'Tạo mật khẩu'
              : 'Cập nhật mật khẩu'
          }
          onPress={onSubmit}
          textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
          textStyle={{
            ...TypoSkin.buttonText1,
            color: ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          containerStyle={{
            height: 48,
            flex: 1,
            borderRadius: 8,
            marginHorizontal: 16,
          }}
          borderColor={ColorThemes.light.Neutral_Border_Color_Main}
          backgroundColor={ColorThemes.light.Primary_Color_Main}
        />
      </WScreenFooter>
    </SafeAreaView>
  );
}
