// App.js
import React, {useEffect, useMemo, useRef, useState} from 'react';
import {useNavigation} from '@react-navigation/native';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Animated,
  ScrollView,
  Image,
} from 'react-native';
import TitleWithBackAction from '../../Screen/Layout/titleWithBackAction';
import {navigate, navigateBack, RootScreen} from '../../router/router';
import {ColorThemes} from '../../assets/skin/colors';
import ScreenHeader from '../../Screen/Layout/header';
import {TypoSkin} from '../../assets/skin/typography';
import {Winicon, SkeletonImage} from 'wini-mobile-components';
import Sound from 'react-native-sound';
import {flashCardDA} from './da';
import ConfigAPI from '../../Config/ConfigAPI';
import {SafeAreaView} from 'react-native-safe-area-context';
import {BaseDA} from '../../base/BaseDA';
import RenderHTML from 'react-native-render-html';

// Màn hình chi tiết bộ thẻ
export const DeckDetailScreen = ({route}: any) => {
  const {item} = route.params;
  const [listDetail, setListDetail] = useState<any[]>(item.lstDetail);
  const [learnedCards, setLearnedCards] = useState<Set<string>>(new Set());
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayingId, setCurrentPlayingId] = useState<string | null>(null);
  const da = new flashCardDA();
  const soundRef = useRef<Sound | null>(null);

  // Hàm phát audio
  const playAudio = (audioUrl: string, cardId: string) => {
    // Stop any currently playing sound
    stopAudio();

    // Set up sound
    Sound.setCategory('Playback');

    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      if (error) {
        console.log('Failed to load sound', error);
        setIsPlaying(false);
        setCurrentPlayingId(null);
        return;
      }

      // Play the sound
      setIsPlaying(true);
      setCurrentPlayingId(cardId);
      sound.play(success => {
        if (success) {
          console.log('Sound played successfully');
        } else {
          console.log('Sound playback failed');
        }
        setIsPlaying(false);
        setCurrentPlayingId(null);
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Hàm dừng audio
  const stopAudio = () => {
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
      setIsPlaying(false);
      setCurrentPlayingId(null);
    }
  };
  useEffect(() => {
    // Lấy danh sách thẻ đã học từ
    mapingData();
  }, []);
  // Hàm map dữ liệu
  const mapingData = async () => {
    const list = await Promise.all(
      item.lstDetail.map(async (card: any) => {
        const isLearned = await da.checkCardIsLearned(card.Id);
        return {
          ...card,
          isLearned,
        };
      }),
    );
    setListDetail(list);
  };
  const navigation = useNavigation<any>();
  useEffect(() => {
    // Lấy danh sách thẻ đã học từ
    const unsubscribe = navigation.addListener('focus', () => {
      mapingData();
    });
    return unsubscribe;
  }, [navigation]);

  return (
    <TitleWithBackAction onBack={() => navigateBack()}>
      <ScrollView style={{padding: 16, flex: 1}}>
        <Text style={styles.headerTitle}>{item.Name}</Text>
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
          <Winicon
            src="outline/files/document-copy"
            size={12}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
          <Text
            style={{
              ...TypoSkin.subtitle4,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              marginRight: 8,
            }}>
            {`${listDetail?.length ?? 0} từ`}
          </Text>
          <Winicon
            src="outline/user interface/view"
            size={12}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
          <Text
            style={{
              ...TypoSkin.subtitle4,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              marginRight: 8,
            }}>
            {`${item.Count ?? 0} người học`}
          </Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'flex-start',
              backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
              paddingHorizontal: 8,
              borderRadius: 24,
              borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
              borderWidth: 1,
              paddingVertical: 4,
              gap: 4,
            }}>
            <Text
              style={{
                fontSize: 12,
                color: '#61616B',
              }}>
              {item.Topic}
            </Text>
          </View>
        </View>
        <View style={styles.learningModes}>
          <Text style={styles.sectionTitle}>Bắt đầu học</Text>

          <View style={styles.modeButtonsContainer}>
            <TouchableOpacity
              style={styles.modeButton}
              onPress={() => {
                if (item.lstDetail?.length > 0) {
                  navigate(RootScreen.FlashcardMode, {list: listDetail,isPublic: item.IsPublic});
                }
              }}>
              <Text style={styles.modeButtonText}>Flashcard</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Danh sách thẻ</Text>
        {listDetail?.map((card: any, index: number) => {
          const isLearned = card.isLearned;
          const isCurrentlyPlaying = currentPlayingId === card.Id && isPlaying;

          return (
            <View key={card.Id} style={styles.enhancedCardItem}>
              {/* Header với ảnh, tiêu đề và actions */}
              <View style={styles.cardHeader}>
                {/* Ảnh minh họa */}
                <View style={styles.imageContainer}>
                  {card.Img ? (
                    <SkeletonImage
                      source={{
                        uri: card.Img.includes('http')
                          ? card.Img
                          : `${ConfigAPI.urlImg}${card.Img}`,
                      }}
                      style={styles.cardImage}
                    />
                  ) : (
                    <View style={styles.placeholderImage}>
                      <Winicon
                        src="outline/files/image"
                        size={20}
                        color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      />
                    </View>
                  )}
                </View>

                {/* Nội dung chính */}
                <View style={styles.cardContent}>
                  <Text style={styles.cardTitle}>
                    {index + 1}. {card.Name}
                  </Text>
                  <Text style={styles.cardDefinition} numberOfLines={2}>
                    {card.Define}
                  </Text>
                  {card.Example && (
                    <Text style={styles.cardExample} numberOfLines={1}>
                      Ví dụ: {card.Example}
                    </Text>
                  )}
                </View>

                {/* Action buttons */}
                <View style={styles.actionButtons}>
                  {/* Learned status indicator - chỉ hiển thị */}
                  <View
                    style={[
                      styles.learnedIndicator,
                      isLearned && styles.learnedIndicatorActive,
                    ]}>
                    {isLearned && (
                      <Text
                        style={{
                          ...TypoSkin.subtitle4,
                          color: ColorThemes.light.Success_Color_Main,
                        }}>
                        Đã học
                      </Text>
                    )}
                  </View>

                  {/* Audio button */}
                  {card.Audio && (
                    <TouchableOpacity
                      style={[styles.audioButton]}
                      onPress={() => {
                        if (isCurrentlyPlaying) {
                          stopAudio();
                        } else {
                          const audioUrl = card.Audio.includes('http')
                            ? card.Audio
                            : `${ConfigAPI.urlImg}${card.Audio}`;
                          playAudio(audioUrl, card.Id);
                        }
                      }}>
                      <Winicon
                        src={
                          isCurrentlyPlaying
                            ? 'color/multimedia/button-pause'
                            : 'fill/multimedia/sound'
                        }
                        size={16}
                        color="#61616B"
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
          );
        })}
        <View style={{height: 50}} />
      </ScrollView>
    </TitleWithBackAction>
  );
};

const {width, height} = Dimensions.get('window');

// Màn hình chế độ Flashcard với animation
export const FlashcardModeScreen = ({route}: any) => {
  const {list, isPublic} = route.params;
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showFront, setShowFront] = useState(true);
  const [showTranscription, setShowTranscription] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const navigation = useNavigation<any>();
  // Animated values
  const flipAnimation = useRef(new Animated.Value(0)).current;
  const nextCardOpacity = useRef(new Animated.Value(0)).current;
  const nextCardScale = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    const getAudio = async () => {
      list.map(async (item: any) => {
        if (item.Audio) {
          const rs = await BaseDA.getFilesInfor([item.Audio]);
          if (rs && rs.data && rs.data.length > 0) {
            item.Audio = rs.data[0].Url;
          }
        }
      });
    };
    getAudio();
  }, []);

  // Sound reference
  const soundRef = useRef<Sound | null>(null);

  // Function to play audio
  const playAudio = (audioUrl: string) => {
    // Stop any currently playing sound
    stopAudio();

    // Set up sound
    Sound.setCategory('Playback');

    // Create new sound instance
    const sound = new Sound(audioUrl, '', error => {
      if (error) {
        console.log('Failed to load sound', error);
        setIsPlaying(false);
        return;
      }

      // Play the sound
      setIsPlaying(true);
      sound.play(success => {
        if (success) {
          console.log('Sound played successfully');
        } else {
          console.log('Sound playback failed');
        }
        setIsPlaying(false);
      });
    });

    // Save reference to sound
    soundRef.current = sound;
  };

  // Function to stop audio
  const stopAudio = () => {
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
      setIsPlaying(false);
    }
  };
  const da = useMemo(() => new flashCardDA(), []);

  // Khi lật thẻ
  const flipCard = async () => {
    // Stop any playing audio
    stopAudio();

    // Khởi tạo animation lật
    Animated.spring(flipAnimation, {
      toValue: showFront ? 180 : 0,
      friction: 8,
      tension: 10,
      useNativeDriver: true,
    }).start();

    setShowFront(!showFront);
    // add customerFlashCard
    await da.addCustomerFlashCard({
      FlashCardId: list[currentIndex].FlashCardId,
      FlashCardDetailId: list[currentIndex].Id,
      Name: list[currentIndex].Name,
    });
  };

  // Khi chuyển sang thẻ tiếp theo
  const nextCard = () => {
    if (currentIndex < list?.length - 1) {
      // Stop any playing audio
      stopAudio();

      // Animation chuyển thẻ
      Animated.parallel([
        Animated.timing(nextCardOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(nextCardScale, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setCurrentIndex(currentIndex + 1);
        setShowFront(true);
        flipAnimation.setValue(0);

        // Animation hiện thẻ mới
        Animated.parallel([
          Animated.timing(nextCardOpacity, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
          Animated.timing(nextCardScale, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ]).start();
      });
    }
  };

  // Khi trở lại thẻ trước
  const prevCard = () => {
    if (currentIndex > 0) {
      // Stop any playing audio
      stopAudio();

      // Animation chuyển thẻ
      Animated.parallel([
        Animated.timing(nextCardOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(nextCardScale, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setCurrentIndex(currentIndex - 1);
        setShowFront(true);
        flipAnimation.setValue(0);

        // Animation hiện thẻ mới
        Animated.parallel([
          Animated.timing(nextCardOpacity, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
          Animated.timing(nextCardScale, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ]).start();
      });
    }
  };

  // Tính toán các giá trị transform dựa trên animation
  const frontInterpolate = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: ['0deg', '180deg'],
  });

  const backInterpolate = flipAnimation.interpolate({
    inputRange: [0, 180],
    outputRange: ['180deg', '360deg'],
  });

  // Style cho mặt trước và mặt sau
  const frontAnimatedStyle = {
    transform: [{rotateY: frontInterpolate}],
    opacity: flipAnimation.interpolate({
      inputRange: [89, 90],
      outputRange: [1, 0],
    }),
  };

  const backAnimatedStyle = {
    transform: [{rotateY: backInterpolate}],
    opacity: flipAnimation.interpolate({
      inputRange: [89, 90],
      outputRange: [0, 1],
    }),
  };

  // Style cho card container
  const cardContainerStyle = {
    opacity: nextCardOpacity,
    transform: [{scale: nextCardScale}],
  };

  // Hiệu ứng phát sáng cho gợi ý lật
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    ).start();

    // Set opacity và scale ban đầu là 1 khi màn hình được load
    nextCardOpacity.setValue(1);
    nextCardScale.setValue(1);

    // Cleanup function to release sound resources when component unmounts
    return () => {
      if (soundRef.current) {
        soundRef.current.stop();
        soundRef.current.release();
        soundRef.current = null;
      }
    };
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        title={
          <View style={{alignItems: 'center', paddingTop: 16}}>
            <Text style={styles.progressText}>
              {currentIndex + 1} / {list?.length ?? 1}
            </Text>
          </View>
        }
        titleStyle={{
          ...TypoSkin.heading5,
          color: ColorThemes.light.Neutral_Text_Color_Title,
        }}
        onBack={() => {
          navigateBack();
        }}
      />
      <View style={styles.container}>
        {/* Card Container - chiếm phần lớn màn hình */}
        <Animated.View style={[styles.cardContainer, cardContainerStyle]}>
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={flipCard}
            style={styles.cardTouchable}>
            {/* Mặt trước thẻ */}
            {list[currentIndex].Type === 2 ? <Animated.View
              style={[styles.flashcard, styles.cardFace, frontAnimatedStyle]}>
              {/* Name ở trên cùng */}

              <Text style={styles.frontCardName}>
                {list[currentIndex].Name}
              </Text>
              {/* Hình ảnh ở giữa */}
              <View style={{...styles.frontImageContainer,alignItems:'center'}}>
                   {
                list[currentIndex].Structure && showTranscription && <RenderHTML
                source={{html: list[currentIndex].Structure}}
                baseStyle={{
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle
                }}
                />
              }
              </View>
            </Animated.View> : <Animated.View
              style={[styles.flashcard, styles.cardFace, frontAnimatedStyle]}>
              {/* Name ở trên cùng */}

              <Text style={styles.frontCardName}>
                {list[currentIndex].Name}
              </Text>
              {
                list[currentIndex].ChinaVietSound && <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {list[currentIndex].ChinaVietSound}
              </Text>
              }
              

              {/* Hình ảnh ở giữa */}
              <View style={styles.frontImageContainer}>
                {list[currentIndex].Img ? (
                  <Image
                    source={{
                      uri: list[currentIndex].Img.includes('http')
                        ? list[currentIndex].Img
                        : `${ConfigAPI.urlImg}${list[currentIndex].Img}`,
                    }}
                    style={styles.frontCardImage}
                    resizeMode="contain"
                  />
                ) : (
                  <View style={styles.frontImagePlaceholder}>
                    <Winicon
                      src="outline/files/image"
                      size={40}
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                    />
                  </View>
                )}
              </View>

              {/* Phiên âm ở dưới */}
              {showTranscription && (
                <View style={styles.transcriptionContainer}>
                  {list[currentIndex].Hiragana && (
                    <View style={styles.transcriptionLeft}>
                      <Text style={styles.transcriptionText}>
                        {list[currentIndex].Hiragana}
                      </Text>
                    </View>
                  )}
                  {list[currentIndex].Katakana && (
                    <View style={styles.transcriptionRight}>
                      <Text style={styles.transcriptionText}>
                        {list[currentIndex].Katakana}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </Animated.View>}
            {/* Mặt sau thẻ */}
            {
              list[currentIndex].Type === 2 ? 
              <Animated.View
              style={[styles.flashcard, styles.cardFace, backAnimatedStyle]}>
              {/* Name ở trên cùng */}
              <Text style={styles.backCardName}>{list[currentIndex].Name}</Text>

              {/* Audio icon */}
              {list[currentIndex].Audio && (
                <TouchableOpacity
                  style={styles.backAudioButton}
                  onPress={() => {
                    if (isPlaying) {
                      stopAudio();
                      return;
                    }
                    if (list[currentIndex].Audio) {
                      const audioUrl = list[currentIndex].Audio.includes('http')
                        ? list[currentIndex].Audio
                        : `${ConfigAPI.urlImg}${list[currentIndex].Audio}`;
                      playAudio(audioUrl);
                    }
                  }}>
                  <Winicon
                    src="fill/multimedia/sound"
                    size={18}
                    color={
                      isPlaying
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle
                    }
                  />
                </TouchableOpacity>
              )}

              {/* Content container */}
              <View style={styles.backContentContainer}>
                {/* Definition */}
                <View style={{...styles.backDefinitionContainer,alignItems:'center'}}>
                  <RenderHTML source={{html: list[currentIndex].Explain}}
                  baseStyle={{
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle
                  }}
                  />
                  <Text style={styles.backDefinitionText}>
                    { '•  ' + list[currentIndex].Name + ' : ' + list[currentIndex].Define}
                  </Text>
                </View>

                {/* Example */}
                {list[currentIndex].Example && (
                  <View style={styles.backExampleContainer}>
                    <Text style={styles.backExampleText}>
                      {list[currentIndex].Example}
                    </Text>
                  </View>
                )}
              </View>
            </Animated.View> : <Animated.View
              style={[styles.flashcard, styles.cardFace, backAnimatedStyle]}>
              {/* Name ở trên cùng */}
              <Text style={styles.backCardName}>{list[currentIndex].Name}</Text>

              {/* Audio icon */}
              {list[currentIndex].Audio && (
                <TouchableOpacity
                  style={styles.backAudioButton}
                  onPress={() => {
                    if (isPlaying) {
                      stopAudio();
                      return;
                    }
                    if (list[currentIndex].Audio) {
                      const audioUrl = list[currentIndex].Audio.includes('http')
                        ? list[currentIndex].Audio
                        : `${ConfigAPI.urlImg}${list[currentIndex].Audio}`;
                      playAudio(audioUrl);
                    }
                  }}>
                  <Winicon
                    src="fill/multimedia/sound"
                    size={18}
                    color={
                      isPlaying
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle
                    }
                  />
                </TouchableOpacity>
              )}

              {/* Content container */}
              <View style={styles.backContentContainer}>
                {/* Definition */}
                <View style={styles.backDefinitionContainer}>
                  <Text style={styles.backDefinitionLabel}>
                    {list[currentIndex].Transcription || 'Định nghĩa'}
                  </Text>
                  <Text style={styles.backDefinitionText}>
                    {list[currentIndex].Name + ': ' + list[currentIndex].Define}
                  </Text>
                </View>

                {/* Example */}
                {list[currentIndex].Example && (
                  <View style={styles.backExampleContainer}>
                    <Text style={styles.backExampleText}>
                      {list[currentIndex].Example}
                    </Text>
                  </View>
                )}
              </View>
            </Animated.View>
            }
          </TouchableOpacity>

          {/* Audio và Eye icons - Floating với Animated opacity */}
          <Animated.View
            style={[
              styles.frontIconsContainer,
              {
                opacity: flipAnimation.interpolate({
                  inputRange: [0, 90, 180],
                  outputRange: [1, 0, 0],
                }),
                pointerEvents: showFront ? 'auto' : 'none',
                top: list[currentIndex].ChinaVietSound ? 90 : 60
              },
            ]}>
            {list[currentIndex].Audio && (
              <TouchableOpacity
                style={styles.frontAudioButton}
                onPress={() => {
                  if (isPlaying) {
                    stopAudio();
                    return;
                  }
                  if (list[currentIndex].Audio) {
                    const audioUrl = list[currentIndex].Audio.includes('http')
                      ? list[currentIndex].Audio
                      : `${ConfigAPI.url.replace('/api/', '')}${
                          list[currentIndex].Audio
                        }`;
                    playAudio(audioUrl);
                  }
                }}>
                <Winicon
                  src="fill/multimedia/sound"
                  size={18}
                  color={
                    isPlaying
                      ? ColorThemes.light.Primary_Color_Main
                      : ColorThemes.light.Neutral_Text_Color_Subtitle
                  }
                />
              </TouchableOpacity>
            )}

            {(list[currentIndex].Hiragana || list[currentIndex].Katakana || list[currentIndex].Structure) && (
              <TouchableOpacity
                style={{...styles.frontEyeButton}}
                onPress={() => {
                  setShowTranscription(!showTranscription);
                }}>
                {!showTranscription ? (
                  <Winicon
                    src="fill/layout/eye"
                    size={18}
                    color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  />
                ) : (
                  <Winicon
                    src="fill/layout/eye-2-slash"
                    size={18}
                    color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  />
                )}
                {/* <Winicon
                  src="outline/user interface/eye"
                  size={20}
                  color={showTranscription
                    ? ColorThemes.light.Primary_Color_Main
                    : ColorThemes.light.Neutral_Text_Color_Subtitle
                  }
                /> */}
              </TouchableOpacity>
            )}
          </Animated.View>
        </Animated.View>

        {/* Bottom Controls Container */}
        <View style={styles.bottomControlsContainer}>
          <View style={styles.navigationButtons}>
            <TouchableOpacity
              style={[
                styles.arrowButton,
                styles.prevButton,
                currentIndex === 0 && styles.disabledArrowButton,
              ]}
              onPress={prevCard}
              disabled={currentIndex === 0}>
              <Winicon
                src="color/arrows/arrow-left-3"
                size={20}
                color={
                  currentIndex === 0
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Primary_Color_Main
                }
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.arrowButton,
                styles.nextButton,
                currentIndex === list.length - 1 && styles.disabledArrowButton,
              ]}
              onPress={nextCard}
              disabled={currentIndex === list.length - 1}>
              <Winicon
                src="color/arrows/arrow-right-3"
                size={20}
                color={
                  currentIndex === list.length - 1
                    ? ColorThemes.light.Neutral_Text_Color_Subtitle
                    : ColorThemes.light.Primary_Color_Main
                }
              />
            </TouchableOpacity>
          </View>

          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {width: `${((currentIndex + 1) / list.length) * 100}%`},
              ]}
            />
          </View>

          <TouchableOpacity
            style={styles.button}
            onPress={() => {
              navigation.push(RootScreen.StartQuizGameFlascard, {lst: list, isPublic: isPublic });
            }}>
            <Text style={styles.buttonText}>Kiểm tra từ mới</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

// Styles
const styles = StyleSheet.create({
  progressBar: {
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginTop: 24,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4C7BF4',
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 24,
    transform: [{perspective: 1000}], // Cần thiết cho hiệu ứng 3D
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  cardTouchable: {
    width: '100%',
    height: '100%',
  },
  flashcard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'flex-start',
    padding: 24,
    height: '100%',
    width: '100%',
    // shadowColor: '#000',
    // shadowOffset: {width: 0, height: 2},
    // shadowOpacity: 0.2,
    // shadowRadius: 4,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    backfaceVisibility: 'hidden', // Quan trọng cho hiệu ứng lật
  },
  cardFace: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
  },
  flashcardText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  flashcardText1: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  flipHintContainer: {
    position: 'absolute',
    bottom: 16,
  },
  flipHint: {
    color: '#999',
    fontSize: 12,
  },
  container: {
    flex: 1,
    marginHorizontal: 16,
    justifyContent: 'space-between',
  },
  bottomControlsContainer: {
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  subTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  deckItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  deckTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  deckCount: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  button: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: 'bold',
    fontSize: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 24,
    marginBottom: 12,
    color: '#333',
  },
  cardItem: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 8,
    marginBottom: 16,
  },
  enhancedCardItem: {
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  imageContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
  },
  cardImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  cardContent: {
    flex: 1,
    gap: 4,
  },
  cardTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: '600',
  },
  cardDefinition: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    lineHeight: 20,
  },
  cardExample: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    fontStyle: 'italic',
  },
  actionButtons: {
    flexDirection: 'column',
    gap: 8,
    alignItems: 'center',
  },
  audioButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,

    // borderColor: ColorThemes.light.Primary_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
  },
  audioButtonActive: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
  learnedIndicator: {
    width: 75,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
  },
  learnedIndicatorActive: {
    // cho background rgb nhạt hơn chút.
    backgroundColor: ColorThemes.light.Success_Color_Background,
    borderRadius: 14,
  },
  cardFront: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },

  learningModes: {
    marginVertical: 16,
  },
  modeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modeButton: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  modeButtonText: {
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: 'bold',
  },

  navigationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // marginBottom: 16,
    paddingHorizontal: 40,
  },
  navButton: {
    backgroundColor: '#e0e0e0',
    padding: 12,
    borderRadius: 8,
    width: '48%',
    alignItems: 'center',
  },
  navButtonText: {
    color: '#333',
    fontWeight: 'bold',
  },
  arrowButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    alignItems: 'center',
    justifyContent: 'center',

    borderWidth: 1,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  prevButton: {
    // Có thể thêm style riêng cho nút trước nếu cần
  },
  nextButton: {
    // Có thể thêm style riêng cho nút tiếp nếu cần
  },
  disabledArrowButton: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderColor: ColorThemes.light.Neutral_Text_Color_Subtitle,
    shadowOpacity: 0.05,
  },
  progressText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  learnCardQuestion: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  learnCardAnswer: {
    fontSize: 18,
    color: '#4C7BF4',
    textAlign: 'center',
    marginBottom: 24,
    fontWeight: 'bold',
  },
  showAnswerButton: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 24,
  },
  showAnswerButtonText: {
    color: '#4C7BF4',
    fontWeight: 'bold',
  },
  feedbackButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  feedbackButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  incorrectButton: {
    backgroundColor: '#ff6b6b',
  },
  correctButton: {
    backgroundColor: '#4ecdc4',
  },
  feedbackButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  resultText: {
    fontSize: 18,
    textAlign: 'center',
    marginVertical: 24,
    color: '#333',
  },
  // New styles for enhanced flashcard
  eyeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  frontCardName: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  frontIconsContainer: {
    position: 'absolute',
    top: 85,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    zIndex: 10,
  },
  frontAudioButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  frontEyeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },

  frontImageContainer: {
    width: 300,
    height: 300,
    borderRadius: 12,
    overflow: 'hidden',
    // marginBottom: 20,
    marginTop: 35,
    alignSelf: 'center',
  },
  frontCardImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    // marginTop: 40,
  },
  frontImagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
  },
  transcriptionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    position: 'absolute',
    bottom: 60,
  },
  transcriptionLeft: {
    flex: 1,
    alignItems: 'flex-start',
  },
  transcriptionRight: {
    flex: 1,
    alignItems: 'flex-end',
  },
  transcriptionText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    textAlign: 'center',
    lineHeight: 24,
  },
  backCardName: {
    ...TypoSkin.heading4,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 16,
  },
  backAudioButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  backContentContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    paddingHorizontal: 20,
    marginTop: 8,
  },
  backDefinitionContainer: {
    marginBottom: 16,
  },
  backDefinitionLabel: {
    ...TypoSkin.subtitle2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 8,
    fontWeight: '600',
  },
  backDefinitionText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    lineHeight: 24,
    textAlign: 'left',
  },
  backExampleContainer: {
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  backExampleText: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    fontStyle: 'italic',
    lineHeight: 22,
    textAlign: 'left',
  },
});
