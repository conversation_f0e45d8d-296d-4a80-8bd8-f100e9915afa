/* eslint-disable react-native/no-inline-styles */
import {
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
  Image,
} from 'react-native';
import React, {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FBottomSheet,
  Winicon,
  showSnackbar,
  ComponentStatus,
} from 'wini-mobile-components';
import {TabView, TabBar} from 'react-native-tab-view';
import {ColorThemes} from '../../assets/skin/colors';
import {navigate, RootScreen} from '../../router/router';
import EmptyPage from '../../Screen/emptyPage';
import {flashCardDA} from './da';
import {useNavigation} from '@react-navigation/native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {ScrollView} from 'react-native-gesture-handler';
import {TypoSkin} from '../../assets/skin/typography';
import {CustomerDA} from '../customer/da';
import ConfigAPI from '../../Config/ConfigAPI';

// Component cho tab FlashCard phổ biến
const PublicFlashCardsTab = ({
  isLoading,
  data,
  isRefresh,
  onRefresh,
  customerInfo,
  customer,
  t,
}: {
  isLoading: boolean;
  data: any[];
  isRefresh: boolean;
  onRefresh: () => void;
  customerInfo: Map<string, any>;
  customer: any;
  t: any;
}) => (
  <View style={{flex: 1}}>
    {isLoading ? (
      <View style={{gap: 16, paddingTop: 16}}>
        {[1, 2, 3, 4].map((_, idx) => (
          <SkeletonFlashCard key={`skeleton-public-${idx}`} />
        ))}
      </View>
    ) : data?.length ? (
      <ScrollView
        style={{paddingTop: 16}}
        contentContainerStyle={{paddingBottom: 100}}
        refreshControl={
          <RefreshControl refreshing={isRefresh} onRefresh={onRefresh} />
        }>
        <View style={{gap: 16}}>
          {data.map((item: any, idx: number) => (
            <FlashCardItem
              key={idx}
              item={item}
              index={idx}
              customerInfo={customerInfo}
              customer={customer}
              t={t}
            />
          ))}
        </View>
      </ScrollView>
    ) : (
      <EmptyPage title={t('common.noData')} />
    )}
  </View>
);

// Component cho tab FlashCard của tôi
const MyFlashCardsTab = ({
  isLoading,
  data,
  isRefresh,
  onRefresh,
  customerInfo,
  customer,
  onEdit,
  onDelete,
  t,
}: {
  isLoading: boolean;
  data: any[];
  isRefresh: boolean;
  onRefresh: () => void;
  customerInfo: Map<string, any>;
  customer: any;
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
  t: any;
}) => (
  <View style={{flex: 1}}>
    {isLoading ? (
      <View style={{gap: 16, paddingTop: 16}}>
        {[1, 2, 3, 4].map((_, idx) => (
          <SkeletonFlashCard key={`skeleton-my-${idx}`} />
        ))}
      </View>
    ) : data?.length ? (
      <ScrollView
        style={{paddingTop: 16}}
        contentContainerStyle={{paddingBottom: 100}}
        refreshControl={
          <RefreshControl refreshing={isRefresh} onRefresh={onRefresh} />
        }>
        <View style={{gap: 16}}>
          {data.map((item: any, idx: number) => (
            <FlashCardItem
              key={idx}
              item={item}
              index={idx}
              customerInfo={customerInfo}
              customer={customer}
              onEdit={onEdit}
              onDelete={onDelete}
              t={t}
            />
          ))}
        </View>
      </ScrollView>
    ) : (
      <EmptyPage title={t('common.noData')} />
    )}
  </View>
);

// Component FlashCard Item tùy chỉnh
const FlashCardItem = ({
  item,
  index,
  customerInfo,
  customer,
  onEdit,
  onDelete,
  t,
}: {
  item: any;
  index: number;
  customerInfo: Map<string, any>;
  customer: any;
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
  t: any;
}) => {
  const creatorInfo = customerInfo.get(item.CustomerId);
  const isMyCard = customer.Id === item.CustomerId;

  const renderSmallAvatar = () => {
    if (creatorInfo?.AvatarUrl) {
      return (
        <Image
          source={{
            uri: creatorInfo.AvatarUrl.includes('http')
              ? creatorInfo.AvatarUrl
              : `${ConfigAPI.urlImg}${creatorInfo.AvatarUrl}`,
          }}
          style={{
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: ColorThemes.light.Neutral_Border_Color_Main,
          }}
        />
      );
    } else {
      return (
        <View
          style={{
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: ColorThemes.light.Primary_Color_Main,
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: 8,
              fontWeight: '500',
              color: ColorThemes.light.Neutral_Background_Color_Absolute,
            }}>
            {creatorInfo?.Name ? creatorInfo.Name.charAt(0).toUpperCase() : 'U'}
          </Text>
        </View>
      );
    }
  };

  return (
    <TouchableOpacity
      key={index}
      onPress={() => {
        navigate(RootScreen.DeckDetail, {item});
      }}
      style={{
        borderColor: ColorThemes.light.Neutral_Border_Color_Main,
        borderWidth: 1,
        marginHorizontal: 16,
        padding: 16,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
      }}>
      {/* Thông tin chính */}
      <View style={{flex: 1, gap: 8}}>
        {/* Tên flashcard */}
        <Text
          style={{
            ...TypoSkin.subtitle1,
            color: ColorThemes.light.Neutral_Text_Color_Title,
          }}>
          {item.Name.toString()}
        </Text>

        {/* Thông tin người tạo */}
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
          {renderSmallAvatar()}
          <Text
            style={{
              ...TypoSkin.subtitle4,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}>
            {creatorInfo?.Name || t('flashcard.defaultUser')}
          </Text>
        </View>

        {/* Số từ học */}
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
          <Winicon
            src="outline/files/document-copy"
            size={12}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
          <Text
            style={{
              ...TypoSkin.subtitle4,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              marginRight: 8,
            }}>
            {t('flashcard.wordsCount', {count: item.lstDetail?.length ?? 0})}
          </Text>
          {!isMyCard ? (
            <>
              <Winicon
                src="outline/user interface/view"
                size={12}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle4,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  marginRight: 8,
                }}>
                {t('flashcard.learnersCount', {count: item.Count ?? 0})}
              </Text>
            </>
          ) : null}
        </View>
      </View>

      {/* Action buttons - chỉ hiển thị cho FlashCard của tôi */}
      {isMyCard && (onEdit || onDelete) && (
        <View
          style={{
            flexDirection: 'row',
            gap: 8,
            alignItems: 'center',
          }}>
          {onEdit && (
            <TouchableOpacity
              style={{
                padding: 8,
                borderRadius: 20,
                // backgroundColor: ColorThemes.light.Primary_Color_Main,
              }}
              onPress={e => {
                e.stopPropagation(); // Ngăn trigger onPress của TouchableOpacity cha
                onEdit(item);
              }}>
              <Winicon
                src="outline/user interface/edit"
                color="#000"
                size={16}
              />
            </TouchableOpacity>
          )}
          {onDelete && (
            <TouchableOpacity
              style={{
                padding: 8,
                borderRadius: 20,
              }}
              onPress={e => {
                e.stopPropagation(); // Ngăn trigger onPress của TouchableOpacity cha
                onDelete(item);
              }}>
              <Winicon
                src="outline/user interface/trash-can"
                color="red"
                size={16}
              />
            </TouchableOpacity>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default function FlashCards() {
  const {t} = useTranslation();
  const [myCards, setMyCards] = useState<any[]>([]);
  const [publicCards, setPublicCards] = useState<any[]>([]);
  const [isLoadingMy, setLoadingMy] = useState(false);
  const [isLoadingPublic, setLoadingPublic] = useState(false);
  const [isRefreshMy, setRefreshMy] = useState(false);
  const [isRefreshPublic, setRefreshPublic] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<Map<string, any>>(new Map());
  const [index, setIndex] = useState(0);
  const [routes] = useState([
    {key: 'public', title: t('flashcard.publicFlashCards')},
    {key: 'my', title: t('flashcard.myFlashCards')},
  ]);

  const da = useMemo(() => new flashCardDA(), []);
  const customerDA = useMemo(() => new CustomerDA(), []);
  const bottomSheetRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;

  // Hàm lấy dữ liệu FlashCard phổ biến
  const getPublicData = useCallback(async () => {
    setLoadingPublic(true);
    const result = await da.getListPublicFlashCard();
    if (result) {
      const list = await Promise.all(
        result.data.map(async (item: any) => {
          const detail = await da.getListDetailbyId(item.Id);
          if (detail) {
            return {
              ...item,
              lstDetail: detail?.data,
              Topic: result?.Topic[0]?.Name,
            };
          } else {
            return {
              ...item,
              Topic: result?.Topic[0]?.Name,
            };
          }
        }),
      );

      // Lấy thông tin customer cho các flashcard
      const uniqueCustomerIds = [
        ...new Set(list.map((item: any) => item.CustomerId)),
      ];

      setCustomerInfo(prevCustomerInfo => {
        const newCustomerInfo = new Map(prevCustomerInfo);

        // Chỉ gọi API cho những customer chưa có thông tin
        const customerPromises = uniqueCustomerIds.map(
          async (customerId: string) => {
            if (customerId && !newCustomerInfo.has(customerId)) {
              const customerData = await customerDA.getCustomerItem(customerId);
              if (customerData) {
                setCustomerInfo(current => {
                  const updated = new Map(current);
                  updated.set(customerId, customerData);
                  return updated;
                });
              }
            }
          },
        );

        Promise.all(customerPromises);
        return newCustomerInfo;
      });

      setPublicCards(list);
    }
    setLoadingPublic(false);
    setRefreshPublic(false);
  }, [da, customerDA]);

  // Hàm lấy dữ liệu FlashCard của tôi
  const getMyData = useCallback(async () => {
    setLoadingMy(true);
    const result = await da.getListMyFlashCard();
    if (result) {
      const list = await Promise.all(
        result.data.map(async (item: any) => {
          const detail = await da.getListDetailbyId(item.Id);
          if (detail) {
            return {
              ...item,
              lstDetail: detail?.data,
              Topic: result?.Topic[0]?.Name,
            };
          } else {
            return {
              ...item,
              Topic: result?.Topic[0]?.Name,
            };
          }
        }),
      );

      // Lấy thông tin customer cho các flashcard
      const uniqueCustomerIds = [
        ...new Set(list.map((item: any) => item.CustomerId)),
      ];

      setCustomerInfo(prevCustomerInfo => {
        const newCustomerInfo = new Map(prevCustomerInfo);

        // Chỉ gọi API cho những customer chưa có thông tin
        const customerPromises = uniqueCustomerIds.map(
          async (customerId: string) => {
            if (customerId && !newCustomerInfo.has(customerId)) {
              const customerData = await customerDA.getCustomerItem(customerId);
              if (customerData) {
                setCustomerInfo(current => {
                  const updated = new Map(current);
                  updated.set(customerId, customerData);
                  return updated;
                });
              }
            }
          },
        );

        Promise.all(customerPromises);
        return newCustomerInfo;
      });

      setMyCards(list);
    }
    setLoadingMy(false);
    setRefreshMy(false);
  }, [da, customerDA]);

  // Initial load - chỉ chạy 1 lần khi component mount
  useEffect(() => {
    getPublicData();
    getMyData();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Focus listener - chỉ setup 1 lần
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // Chỉ refresh khi quay lại từ màn hình khác
      getPublicData();
      getMyData();
    });

    // Cleanup function để remove listener khi component unmount
    return unsubscribe;
  }, [navigation]); // eslint-disable-line react-hooks/exhaustive-deps

  // Hàm refresh cho tab FlashCard phổ biến
  const onRefreshPublic = useCallback(async () => {
    setPublicCards([]);
    setCustomerInfo(new Map());
    setRefreshPublic(true);
    await getPublicData();
  }, [getPublicData]);

  // Hàm refresh cho tab FlashCard của tôi
  const onRefreshMy = useCallback(async () => {
    setMyCards([]);
    setCustomerInfo(new Map());
    setRefreshMy(true);
    await getMyData();
  }, [getMyData]);

  // Hàm xử lý sửa FlashCard
  const handleEditFlashCard = useCallback((item: any) => {
    navigate(RootScreen.CreateFlashCard, {
      id: item.Id,
    });
  }, []);

  // Hàm xử lý xóa FlashCard
  const handleDeleteFlashCard = useCallback(async (item: any) => {
    try {
      const result = await da.delete(item.Id);
      if (result) {
        showSnackbar({
          message: t('flashcard.deleteSuccess'),
          status: ComponentStatus.SUCCSESS,
        });
        // Refresh lại danh sách
        // await getMyData();
        setMyCards(prevCards => prevCards.filter(card => card.Id !== item.Id));
      } else {
        showSnackbar({
          message: t('flashcard.deleteError'),
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      showSnackbar({
        message: t('flashcard.deleteError'),
        status: ComponentStatus.ERROR,
      });
    }
  }, []);

  const renderScene = useCallback(
    ({route: sceneRoute}: any) => {
      switch (sceneRoute.key) {
        case 'public':
          return (
            <PublicFlashCardsTab
              isLoading={isLoadingPublic}
              data={publicCards}
              isRefresh={isRefreshPublic}
              onRefresh={onRefreshPublic}
              customerInfo={customerInfo}
              customer={customer}
              t={t}
            />
          );
        case 'my':
          return (
            <MyFlashCardsTab
              isLoading={isLoadingMy}
              data={myCards}
              isRefresh={isRefreshMy}
              onRefresh={onRefreshMy}
              customerInfo={customerInfo}
              customer={customer}
              onEdit={handleEditFlashCard}
              onDelete={handleDeleteFlashCard}
              t={t}
            />
          );
        default:
          return null;
      }
    },
    [
      isLoadingPublic,
      publicCards,
      isRefreshPublic,
      onRefreshPublic,
      isLoadingMy,
      myCards,
      isRefreshMy,
      onRefreshMy,
      customerInfo,
      customer,
      handleEditFlashCard,
      handleDeleteFlashCard,
    ],
  );

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      indicatorStyle={{
        backgroundColor: ColorThemes.light.Primary_Color_Main,
      }}
      style={{
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        elevation: 0,
        shadowOpacity: 0,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
      }}
      labelStyle={{
        ...TypoSkin.subtitle3,
        textTransform: 'none',
      }}
      activeColor={ColorThemes.light.Primary_Color_Main}
      inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
    />
  );

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />

      <TabView
        navigationState={{index, routes}}
        renderScene={renderScene}
        renderTabBar={renderTabBar}
        onIndexChange={setIndex}
        initialLayout={{width: 400}}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={{
          position: 'absolute',
          bottom: 20,
          right: 20,
          width: 56,
          height: 56,
          borderRadius: 28,
          backgroundColor: ColorThemes.light.Primary_Color_Main,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPress={() => {
          navigate(RootScreen.CreateFlashCard);
        }}>
        <Winicon
          src="outline/layout/plus"
          size={24}
          color={ColorThemes.light.Neutral_Background_Color_Absolute}
        />
      </TouchableOpacity>
    </View>
  );
}

const SkeletonFlashCard = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          marginHorizontal: 16,
          marginBottom: 16,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#e0e0e0',
          padding: 16,
        }}>
        {/* Title placeholder */}
        <View
          style={{
            width: '60%',
            height: 18,
            borderRadius: 4,
            marginBottom: 8,
          }}
        />

        {/* Subtitle/count placeholder */}
        <View
          style={{
            width: '30%',
            height: 14,
            borderRadius: 4,
          }}
        />

        {/* Action buttons placeholder - positioned to the right */}
        <View
          style={{
            position: 'absolute',
            right: 16,
            top: 16,
            flexDirection: 'row',
            gap: 8,
          }}>
          <View style={{width: 24, height: 24, borderRadius: 12}} />
          <View style={{width: 24, height: 24, borderRadius: 12}} />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
