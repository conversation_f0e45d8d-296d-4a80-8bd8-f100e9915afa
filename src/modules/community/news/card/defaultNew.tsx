/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {AppButton, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {Ultis} from '../../../../utils/Utils';
import RenderHTML from 'react-native-render-html';
import HTMLProcessor from '../../../../utils/convert';
interface Props {
  containerStyle?: ViewStyle;
  mainContainerStyle?: ViewStyle;
  flexDirection?: 'default' | 'row';
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: any;
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
  onPressLikeAction?: () => void;
  onPressDetail?: () => void;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  horizontalList?: boolean;
  noDivider?: boolean;
  showContent?: boolean;
  dividerColor?: string;
}

export function DefaultNew(props: Props) {
  return (
    <TouchableOpacity
      onPress={props.onPressDetail}
      disabled={props.onPressDetail ? false : true}
      style={[
        {
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          alignSelf: 'flex-start',
          flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
          width: Dimensions.get('screen').width - 32,
          flex: 1,
          ...props.containerStyle,
        },
      ]}>
      <View
        style={[
          stylesDefault.mainContainer,
          {
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
            ...props.mainContainerStyle,
          },
        ]}>
        <View
          style={[
            stylesDefault.img,
            {
              height: props.flexDirection === 'row' ? 88 : 234,
              width:
                props.flexDirection === 'row'
                  ? 88
                  : Dimensions.get('screen').width - 32,
              ...props.imgStyle,
            },
          ]}>
          <Image
            source={{
              uri: props?.data.Img
                ? `${ConfigAPI.urlImg + props?.data.Img}`
                : 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
            }}
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 8,
              objectFit: 'cover',
              position: 'absolute',
            }}
            resizeMode="cover"
          />
          {props.flexDirection === 'default' ? (
            <AppButton
              backgroundColor={'transparent'}
              borderColor="transparent"
              onPress={() => {
                if (props.onPressLikeAction) {
                  props.onPressLikeAction();
                }
              }}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 24,
                width: 24,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Main,
                zIndex: 1,
                position: 'absolute',
                right: 16,
                top: 16,
              }}
              title={<Winicon src="outline/emoticons/heart" size={15} />}
            />
          ) : null}
        </View>
        <View
          style={{
            borderBottomColor:
              props.dividerColor ?? ColorThemes.light.Neutral_Border_Color_Main,
            borderBottomWidth: props.noDivider ? 0 : 1,
            paddingBottom: 8,
            flexDirection: props.flexDirection === 'row' ? 'row' : 'column',
            alignSelf: 'flex-start',
            maxWidth: props.flexDirection === 'row' ? '80%' : '100%',
          }}>
          <View style={[stylesDefault.mainContent]}>
            {/* infor on top */}
            {props.data.relativeUser ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  gap: 4,
                  marginBottom: 4,
                }}>
                {/* infor img */}
                <View
                  style={[
                    {
                      width: 20,
                      height: 20,
                      borderRadius: 100,
                      backgroundColor: 'black',
                    },
                  ]}>
                  <Image
                    source={{
                      uri: props.data.relativeUser.image
                        ? `${ConfigAPI.urlImg + props?.data.Img}`
                        : 'https://reactnative.dev/img/tiny_logo.png',
                    }}
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                </View>
                {/* infor text */}
                <View style={{flex: 1}}>
                  <Text style={[stylesDefault.inforTitle]}>
                    {props.data.relativeUser.title ?? ''}
                  </Text>
                </View>
              </View>
            ) : null}
            {/* title */}
            {props.data.Title && typeof props.data.Title === 'string' ? (
              <Text
                style={[
                  stylesDefault.titleStyle,
                  {
                    paddingBottom: props.data.Description ? 4 : 0,
                    ...props.titleStyle,
                  },
                ]}
                numberOfLines={2}>
                {props.data.Title ?? ''}
              </Text>
            ) : null}
            {/* subtitle */}
            {props.data?.Description ? (
              <View style={{paddingTop: 4, paddingBottom: 8}}>
                <Text
                  style={[stylesDefault.subTitleStyle, props.subtitleStyle]}
                  numberOfLines={props.showContent ? undefined : 2}>
                  {props.data.Description ?? ''}
                </Text>
              </View>
            ) : null}
            {/* {props.data?.Content ? (
              <View
                style={{
                  height: props.showContent ? undefined : 65,
                  overflow: 'hidden',
                }}>
                <Text
                  style={[stylesDefault.bodyContentStyle]}
                  numberOfLines={3}>
                  {props.data.Content.includes('<')
                    ? HTMLProcessor.processHTMLContent(props.data.Content ?? '')
                    : props.data.Content ?? ''}
                </Text>
              </View>
            ) : null} */}
            {props.listItems?.length ? (
              <View style={{width: '100%', paddingTop: 16}}>
                {props.listItems.map((item, index) => {
                  return (
                    <View
                      key={item.Id}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingVertical: 4,
                        gap: 8,
                      }}>
                      <View
                        style={[
                          {
                            width: 16,
                            borderRadius: 100,
                          },
                        ]}>
                        {item.icon ? (
                          <Winicon src={item.icon} size={16} />
                        ) : (
                          <Text style={[stylesDefault.inforTitle]}>*</Text>
                        )}
                      </View>
                      <Text
                        style={[stylesDefault.inforTitle, {color: '#313135'}]}>
                        {item.title}
                      </Text>
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.onPressSeeMore && props.listItems?.length ? (
              <AppButton
                title={'See more'}
                containerStyle={{
                  justifyContent: 'flex-start',
                  alignSelf: 'baseline',
                  marginVertical: 8,
                }}
                backgroundColor={'transparent'}
                textStyle={TypoSkin.buttonText3}
                borderColor="transparent"
                suffixIconSize={16}
                suffixIcon={'outline/arrows/circle-arrow-right'}
                onPress={props.onPressSeeMore}
                textColor={ColorThemes.light.Info_Color_Main}
              />
            ) : null}
            {props.listTags?.length ? (
              <View style={{width: '100%', flexDirection: 'row', gap: 8}}>
                {props.listTags.map((item, index) => {
                  return (
                    <View
                      key={item.Id}
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                        backgroundColor:
                          ColorThemes.light.Neutral_Background_Color_Main,
                        paddingHorizontal: 8,
                        borderRadius: 24,
                        borderColor:
                          ColorThemes.light.Neutral_Border_Color_Bolder,
                        borderWidth: 1,
                        paddingVertical: 4,
                        gap: 4,
                      }}>
                      <Text style={[stylesDefault.inforTitle]}>
                        {item.title}
                      </Text>
                      <Winicon src={'outline/arrows/right-arrow'} size={12} />
                    </View>
                  );
                })}
              </View>
            ) : null}
            {props.flexDirection === 'default' && props.reportContent && (
              <View
                style={{
                  height: 96,
                  backgroundColor: '#f0f0f0',
                  borderRadius: 8,
                  marginVertical: 16,
                  padding: 24,
                  justifyContent: 'space-between',
                  flexDirection: 'row',
                }}>
                <View style={{gap: 4}}>
                  {props.reportContent ? props.reportContent : null}
                </View>
              </View>
            )}
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
              }}>
              {props.actionView ? props.actionView : null}
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export function SkeletonPlaceCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View style={{...stylesDefault.mainContainer, marginBottom: 16}}>
        <View style={stylesDefault.img} />
        <View style={{flex: 1}}>
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
              marginBottom: 6,
              backgroundColor: '#e0e0e0',
            }}
          />
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
              backgroundColor: '#e0e0e0',
            }}
          />
        </View>
      </View>
      <View style={stylesDefault.mainContent}>
        <View
          style={{
            width: '100%',
            height: 16,
            borderRadius: 4,
            marginBottom: 6,
            backgroundColor: '#e0e0e0',
          }}
        />
        <View
          style={{
            width: '80%',
            height: 16,
            borderRadius: 4,
            backgroundColor: '#e0e0e0',
          }}
        />
      </View>
    </SkeletonPlaceholder>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});
