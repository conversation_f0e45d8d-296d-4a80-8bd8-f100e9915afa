import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Animated} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';

interface ProfileTabBarProps {
  activeTab: number;
  onTabChange: (tab: number) => void;
  forEschool?: boolean;
}

const ProfileTabBar: React.FC<ProfileTabBarProps> = ({
  activeTab,
  onTabChange,
  forEschool,
}) => {
  const tabs = [
    {Id: 0, Name: 'Posts'},
    {Id: 2, Name: 'About'},
  ];

  return (
    <Animated.View style={[styles.tabBarContainer]}>
      <View style={styles.tabBar}>
        {forEschool ? (
          <TouchableOpacity
            key={0}
            style={[styles.tabItem, activeTab === 0 && styles.activeTabItem]}
            onPress={() => onTabChange(0)}>
            <Text
              style={[styles.tabText, activeTab === 0 && styles.activeTabText]}>
              About
            </Text>
            {activeTab === 0 && <View style={styles.indicator} />}
          </TouchableOpacity>
        ) : (
          tabs.map(item => {
            return (
              <TouchableOpacity
                key={item.Id}
                style={[
                  styles.tabItem,
                  activeTab === item.Id && styles.activeTabItem,
                ]}
                onPress={() => onTabChange(item.Id)}>
                <Text
                  style={[
                    styles.tabText,
                    activeTab === item.Id && styles.activeTabText,
                  ]}>
                  {item.Name}
                </Text>
                {activeTab === item.Id && <View style={styles.indicator} />}
              </TouchableOpacity>
            );
          })
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    position: 'absolute',
    top: 320, // Bắt đầu dưới header gốc
    left: 0,
    right: 0,
    height: 50,
    backgroundColor: '#fff',
    zIndex: 2,
    borderBottomWidth: 0.5,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    height: '100%',
    alignItems: 'center',
  },
  tabItem: {
    paddingVertical: 15,
    marginRight: 24,
    position: 'relative',
  },
  activeTabItem: {
    // Style cho tab active
  },
  tabText: {
    ...TypoSkin.label4,
  },
  activeTabText: {
    color: ColorThemes.light.Primary_Color_Main,
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1.5,
    backgroundColor: ColorThemes.light.Primary_Color_Main,
  },
});

export default ProfileTabBar;
