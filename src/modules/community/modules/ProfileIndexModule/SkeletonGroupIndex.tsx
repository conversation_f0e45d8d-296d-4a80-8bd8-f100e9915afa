import React from 'react';
import {View, StyleSheet} from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {SkeletonPlaceCard} from '../../../Default/card/defaultPost';

const SkeletonGroupIndex: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Skeleton cho header */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={styles.header}>
          {/* Header với nút back */}
          <View style={styles.headerBar}>
            <View style={styles.headerBackButton} />
          </View>

          {/* Ảnh cover */}
          <View style={styles.coverImage} />

          {/* Thông tin nhóm */}
          <View style={styles.groupInfoContainer}>
            <View style={styles.groupName} />
            <View style={styles.groupSubtitle} />

            {/* Avatar members */}
            <View style={styles.membersContainer}>
              {[1, 2, 3].map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.memberAvatar,
                    {marginLeft: index > 0 ? -10 : 0},
                  ]}
                />
              ))}
            </View>

            {/* Buttons */}
            <View style={styles.buttonsContainer}>
              <View style={styles.button} />
              <View style={styles.button} />
            </View>
          </View>
        </View>
      </SkeletonPlaceholder>

      {/* Skeleton cho tab bar */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={styles.tabBar}>
          <View style={styles.tabItem} />
          <View style={styles.tabItemShort} />
        </View>
      </SkeletonPlaceholder>

      {/* Skeleton cho nội dung */}
      <SkeletonPlaceholder
        backgroundColor="#f0f0f0"
        highlightColor="#e0e0e0"
        speed={800}>
        <View style={styles.contentContainer}>
          {/* Post input */}
          <View style={styles.postInputContainer}>
            <View style={styles.postInputAvatar} />
            <View style={styles.postInput} />
          </View>

          {/* Posts */}
          {[1, 2, 3].map((_, index) => (
            <View key={index} style={styles.postContainer}>
              {/* Header with avatar and name */}
              <SkeletonPlaceCard />
            </View>
          ))}
        </View>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    height: 320,
  },
  headerBar: {
    height: 56,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerBackButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  coverImage: {
    height: 248,
    width: '100%',
  },
  groupInfoContainer: {
    padding: 16,
  },
  groupName: {
    width: 200,
    height: 24,
    borderRadius: 4,
    marginBottom: 8,
  },
  groupSubtitle: {
    width: 120,
    height: 16,
    borderRadius: 4,
    marginBottom: 16,
  },
  membersContainer: {
    flexDirection: 'row',
  },
  memberAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  buttonsContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
  },
  button: {
    flex: 1,
    height: 40,
    borderRadius: 8,
  },
  tabBar: {
    height: 50,
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  tabItem: {
    width: 100,
    height: 20,
    borderRadius: 4,
    marginRight: 24,
  },
  tabItemShort: {
    width: 100,
    height: 20,
    borderRadius: 4,
  },
  contentContainer: {
    padding: 16,
    gap: 16,
  },
  postInputContainer: {
    height: 69,
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  postInputAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  postInput: {
    flex: 1,
    height: 40,
    borderRadius: 8,
  },
  postContainer: {
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
});

export default SkeletonGroupIndex;
