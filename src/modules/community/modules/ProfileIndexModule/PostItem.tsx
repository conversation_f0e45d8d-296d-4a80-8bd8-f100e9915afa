import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {
  AppButton,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {DefaultPost} from '../../card/defaultPost';
import {dialogCheckAcc} from '../../../../Screen/Layout/mainLayout';
import {navigate, RootScreen} from '../../../../router/router';
import {onShare} from '../../../../features/share';
import {useDispatch} from 'react-redux';
import store, {AppDispatch} from '../../../../redux/store/store';
import {myFeedActions} from '../../reducers/MyFeedReducer';
import {newsFeedActions} from '../../reducers/newsFeedReducer';
import {GroupPostsActions} from '../../reducers/groupPostsReducer';
import {Ultis} from '../../../../utils/Utils';
import {useNavigation} from '@react-navigation/native';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';

interface PostItemProps {
  item: any;
  user: any;
  dialogRef: any;
  bottomSheetRef: any;
}

const PostItem: React.FC<PostItemProps> = ({
  item,
  user,
  dialogRef,
  bottomSheetRef,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const profile = user; // For compatibility with existing code

  const actionView = (
    <View style={styles.postActionContainer}>
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={styles.actionButton}
        onPress={async () => {
          if (user) {
            dispatch(myFeedActions.updateLike(item.Id, item.IsLike === true));
            const newfeed = store
              .getState()
              .newsFeed.data.find((feed: any) => feed.Id === item.Id);
            if (newfeed) {
              dispatch(newsFeedActions.setLike(newfeed.Id, !item.IsLike));
            }
          } else {
            dialogCheckAcc(dialogRef);
          }
        }}
        title={<Text style={styles.actionButtonText}>{item.Likes ?? 0}</Text>}
        textColor={
          item.IsLike === true
            ? ColorThemes.light.Error_Color_Main
            : ColorThemes.light.Neutral_Text_Color_Subtitle
        }
        prefixIconSize={12}
        prefixIcon={
          item.IsLike === true
            ? 'fill/emoticons/heart'
            : 'outline/emoticons/heart'
        }
      />
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={styles.actionButton}
        onPress={async () => {
          navigate(RootScreen.PostDetail, {item: item});
        }}
        prefixIcon={'outline/user interface/b-comment'}
        prefixIconSize={12}
        textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
        title={<Text style={styles.actionButtonText}>{item.Comment ?? 0}</Text>}
      />
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={styles.actionButton}
        onPress={async () => {
          onShare({content: 'Hello world'});
        }}
        prefixIcon={'fill/arrows/social-sharing'}
        prefixIconSize={12}
        textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      />
    </View>
  );

  const trailingView = (
    <View style={styles.postTrailingView}>
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        onPress={async () => {
          if (user) {
            dispatch(
              myFeedActions.addBookmark(item.Id, item.IsBookmark === true),
            );
            const newfeed = store
              .getState()
              .newsFeed.data.find((feed: any) => feed.Id === item.Id);
            if (newfeed) {
              dispatch(
                newsFeedActions.setBookmark(newfeed.Id, !item.IsBookmark),
              );
            }
          } else {
            dialogCheckAcc(dialogRef);
          }
        }}
        containerStyle={styles.trailingButton}
        title={
          <Winicon
            src={
              item.IsBookmark === true
                ? 'fill/user interface/bookmark'
                : 'outline/user interface/bookmark'
            }
            size={14}
            color={
              item.IsBookmark === true
                ? ColorThemes.light.Warning_Color_Main
                : ColorThemes.light.Neutral_Text_Color_Subtitle
            }
          />
        }
      />
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        onPress={() => {
          showBottomSheet({
            ref: bottomSheetRef,
            enableDismiss: true,
            title: 'Actions',
            suffixAction: <View />,
            prefixAction: (
              <TouchableOpacity
                onPress={() => hideBottomSheet(bottomSheetRef)}
                style={styles.bottomSheetCloseButton}>
                <Winicon
                  src="outline/layout/xmark"
                  size={20}
                  color={ColorThemes.light.Neutral_Text_Color_Body}
                />
              </TouchableOpacity>
            ),
            children: (
              <View style={styles.bottomSheetMenuContainer}>
                {profile.Id === customer.Id && (
                  <ListTile
                    onPress={() => {
                      hideBottomSheet(bottomSheetRef);
                      navigation.push(RootScreen.createPost, {
                        editPost: item,
                        groupId: item.GroupId,
                      });
                    }}
                    title={'Edit post'}
                    titleStyle={styles.bottomSheetMenuText}
                  />
                )}
                {profile.Id === customer.Id && (
                  <ListTile
                    onPress={() => {
                      hideBottomSheet(bottomSheetRef);
                      dispatch(myFeedActions.deletePost(item));
                      const newfeed = store
                        .getState()
                        .newsFeed.data.find((a: any) => a.Id === item.Id);
                      if (newfeed) {
                        dispatch(newsFeedActions.hidePostNocall(newfeed.Id));
                      }
                      if (item.GroupId) {
                        const groupfeed = store
                          .getState()
                          .groupPosts.byGroupId[item.GroupId].posts.find(
                            (a: any) => a.Id === item.Id,
                          );
                        if (groupfeed) {
                          dispatch(
                            GroupPostsActions.hidePostNocall(
                              item.GroupId,
                              item.Id,
                            ),
                          );
                        }
                      }
                    }}
                    title={'Delete post'}
                    titleStyle={styles.bottomSheetMenuText}
                  />
                )}
              </View>
            ),
          });
        }}
        containerStyle={styles.trailingButton}
        title={
          <Winicon
            src={'fill/user interface/menu-dots'}
            size={14}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
        }
      />
    </View>
  );

  return (
    <DefaultPost
      data={{
        ...item,
        relativeUser: {
          image: user?.AvatarUrl,
          title: user?.Name,
          subtitle: Ultis.getDiffrentTime(item.DateCreated),
        },
      }}
      containerStyle={styles.defaultPostContainer}
      onPressDetail={() =>
        navigate(RootScreen.PostDetail, {
          item: {
            ...item,
            relativeUser: {
              image: user?.AvatarUrl,
              title: user?.Name,
              subtitle: Ultis.getDiffrentTime(item.DateCreated),
            },
          },
        })
      }
      actionView={actionView}
      trailingView={trailingView}
      showContent={true}
    />
  );
};

const styles = StyleSheet.create({
  postActionContainer: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 4,
    height: 24,
    paddingVertical: 0,
    paddingHorizontal: 8,
  },
  actionButtonText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  postTrailingView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  trailingButton: {
    borderRadius: 100,
    padding: 6,
    height: 24,
    width: 24,
  },
  bottomSheetCloseButton: {
    padding: 6,
    alignItems: 'center',
  },
  bottomSheetMenuContainer: {
    height: Dimensions.get('window').height / 4.5,
    width: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  bottomSheetMenuText: {
    ...TypoSkin.body3,
  },
  defaultPostContainer: {
    paddingHorizontal: 0,
  },
});

export default PostItem;
