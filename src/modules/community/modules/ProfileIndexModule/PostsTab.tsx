import React, {useRef, useState} from 'react';
import {
  View,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Text,
  StyleSheet,
} from 'react-native';
import {FBottomSheet, FDialog} from 'wini-mobile-components';
import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {SkeletonPlaceCard} from '../../../Default/card/defaultPost';
import {useSelectorCustomerState} from '../../../../redux/hook/customerHook';
import {useDispatch} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../../router/router';
import EmptyPage from '../../../../Screen/emptyPage';
import {useMyFeedData} from '../../hook/MyFeedHook';
import {myFeedActions} from '../../reducers/MyFeedReducer';
import PostItem from './PostItem';

interface PostsTabProps {
  profile: any;
}

const PostsTab: React.FC<PostsTabProps> = ({profile}) => {
  const dialogRef = useRef<any>(null);
  const dispatch = useDispatch<any>();
  const size = 20;
  const {data, loading, page} = useMyFeedData(1, size, profile?.Id);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);

  const handleRefresh = async () => {
    if (!loading) {
      setIsRefreshing(true);
      setHasMore(true);
      try {
        await dispatch(myFeedActions.getNewFeed(1, size, profile?.Id));
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
        setTimeout(() => {
          setIsRefreshing(false);
        }, 1000);
      }
    }
  };

  const handleLoadMore = async () => {
    if (!loading && !isRefreshing && hasMore) {
      try {
        const result = myFeedActions.getNewFeed(page + 1, size, profile?.Id);

        if ((result as any)?.payload?.length < size) {
          setHasMore(false);
        } else if ((result as any)?.payload?.length === 0) {
          setHasMore(false);
        }
      } catch (error) {
        console.error('Load more error:', error);
        setHasMore(false);
      }
    }
  };

  return (
    <View style={styles.postsTabContainer}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      {profile?.Id === customer?.Id && (
        <View style={styles.createPostContainer}>
          <View style={styles.createPostInnerContainer}>
            <TouchableOpacity
              style={styles.createPostInputButton}
              onPress={() => {
                navigation.push(RootScreen.createPost, {groupId: null});
              }}>
              <Text style={styles.createPostPlaceholder}>
                Bạn đang nghĩ gì ?
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
      <FlatList
        scrollEnabled={false}
        data={data}
        renderItem={({item}) => (
          <PostItem
            item={item}
            user={profile}
            dialogRef={dialogRef}
            bottomSheetRef={bottomSheetRef}
          />
        )}
        keyExtractor={item => item.Id.toString()}
        showsVerticalScrollIndicator={false}
        style={styles.postsFlatList}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.Primary_Color_Main]}
            tintColor={ColorThemes.light.Primary_Color_Main}
          />
        }
        contentContainerStyle={[
          styles.postsFlatListContent,
          {
            gap:
              data?.filter(item => item.CustomerId === profile?.Id).length > 0
                ? 8
                : 0,
          },
        ]}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading) {
            return (
              <View style={styles.listEmptyLoading}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlaceCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          }
          return (
            <View style={styles.listEmptyContainer}>
              <EmptyPage title="Không có dữ liệu" />
            </View>
          );
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return <SkeletonPlaceCard />;
          }
          if (!hasMore && data.length > 0) {
            return (
              <View style={styles.listFooterContainer}>
                <Text style={styles.listFooterText}>Không còn dữ liệu</Text>
              </View>
            );
          }
          return null;
        }}
      />
      <View style={styles.postsTabSpacer} />
    </View>
  );
};

const styles = StyleSheet.create({
  postsTabContainer: {
    flex: 1,
  },
  createPostContainer: {
    borderRadius: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    marginTop: 8,
    gap: 8,
    padding: 16,
    alignContent: 'center',
    justifyContent: 'center',
  },
  createPostInnerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    width: '100%',
  },
  createPostInputButton: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 8,
  },
  createPostPlaceholder: {
    ...TypoSkin.placeholder1,
    color: ColorThemes.light.Neutral_Text_Color_Placeholder,
    paddingHorizontal: 12,
  },
  postsFlatList: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  postsFlatListContent: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  listEmptyLoading: {
    gap: 8,
  },
  listEmptyContainer: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  listFooterContainer: {
    padding: 16,
    alignItems: 'center',
  },
  listFooterText: {
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    ...TypoSkin.subtitle2,
  },
  postsTabSpacer: {
    height: 60,
  },
});

export default PostsTab;
