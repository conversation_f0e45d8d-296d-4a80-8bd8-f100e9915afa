import React from 'react';
import {View, ScrollView, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import TabItem from './TabItem';
import {useTranslation} from 'react-i18next';

interface TabBarProps {
  activeTab: number;
  onTabPress: (tabId: number) => void;
  flatListRef: React.RefObject<any>;
}

const getTabs = (t: any) => [
  {Id: 0, Name: t('community.tabs.popular'), Icon: 'outline/arrows/trend-up'},
  {Id: 1, Name: t('community.tabs.forYou'), Icon: 'outline/files/news'},
  {
    Id: 2,
    Name: t('community.tabs.following'),
    Icon: 'outline/editing/list-favs-2',
  },
  {
    Id: 3,
    Name: t('community.tabs.bookmark'),
    Icon: 'outline/user interface/bookmark',
  },
];

const TabBar = React.memo(
  ({activeTab, onTabPress, flatListRef}: TabBarProps) => {
    const {t} = useTranslation();
    const tabs = getTabs(t);

    return (
      <View>
        <ScrollView
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          contentContainerStyle={{
            gap: 8,
          }}
          style={styles.tabBar}>
          {tabs.map((tab, index) => (
            <TabItem
              key={`tab-${tab.Id}`}
              tab={tab}
              index={tab.Id}
              isActive={activeTab === tab.Id}
              onPress={() => {
                onTabPress(tab.Id);
                flatListRef.current?.scrollToOffset({
                  animated: false,
                  offset: 0,
                });
              }}
            />
          ))}
        </ScrollView>
      </View>
    );
  },
);

export default TabBar;

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
});
