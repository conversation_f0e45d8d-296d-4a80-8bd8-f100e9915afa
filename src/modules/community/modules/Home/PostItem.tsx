import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  ViewStyle,
  TextStyle,
  StyleSheet,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {useDispatch} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {useNavigation} from '@react-navigation/native';

import {ColorThemes} from '../../../../assets/skin/colors';
import {TypoSkin} from '../../../../assets/skin/typography';
import {DefaultPost} from '../../card/defaultPost';
import {navigate, RootScreen} from '../../../../router/router';
import {Ultis} from '../../../../utils/Utils';
import {onShare} from '../../../../features/share';
import {AppDispatch} from '../../../../redux/store/store';
import {newsFeedActions} from '../../reducers/newsFeedReducer';
import {dialogCheckAcc} from '../../../../Screen/Layout/mainLayout';

// Types
interface PostItemProps {
  item: any;
  user: any;
  dialogRef: any;
  bottomSheetRef: React.RefObject<any>;
}

const PostItem: React.FC<PostItemProps> = ({
  item,
  user,
  dialogRef,
  bottomSheetRef,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const {t} = useTranslation();
  const navigation = useNavigation<any>();

  // Xử lý sự kiện thích bài viết
  const handleLike = async () => {
    if (user) {
      await dispatch(newsFeedActions.updateLike(item.Id, item.IsLike === true));
    } else {
      dialogCheckAcc(dialogRef);
    }
  };

  // Xử lý sự kiện lưu bài viết (đánh dấu)
  const handleBookmark = async () => {
    if (user) {
      await dispatch(
        newsFeedActions.addBookmark(item.Id, item.IsBookmark === true),
      );
    } else {
      dialogCheckAcc(dialogRef);
    }
  };

  // Xử lý sự kiện chia sẻ bài viết
  const handleShare = async () => {
    onShare({content: 'Hello world'});
  };

  // Xử lý sự kiện nhấn vào header (thông tin người đăng/nhóm)
  const handleHeaderPress = () => {
    if (item.GroupId) {
      navigate(RootScreen.GroupIndex, {Id: item.GroupId});
    } else {
      navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
    }
  };

  // Xử lý sự kiện nhấn vào chi tiết bài viết
  const handleDetailPress = () => {
    navigate(RootScreen.PostDetail, {item: item});
  };

  // Xử lý sự kiện mở tùy chọn khác (báo cáo, chỉnh sửa, xóa)
  const handleMoreOptions = () => {
    showBottomSheet({
      ref: bottomSheetRef,
      title: t('community.actions'),
      suffixAction: <View />,
      prefixAction: (
        <TouchableOpacity
          onPress={() => hideBottomSheet(bottomSheetRef)}
          style={{padding: 6, alignItems: 'center'}}>
          <Winicon
            src="outline/layout/xmark"
            size={20}
            color={ColorThemes.light.Neutral_Text_Color_Body}
          />
        </TouchableOpacity>
      ),
      children: <BottomSheetContent />,
    });
  };

  // Component nội dung của bottom sheet tùy chọn bài viết
  const BottomSheetContent = () => (
    <View style={styles.bottomSheetContent}>
      <ListTile
        onPress={() => {
          hideBottomSheet(bottomSheetRef);
          showSnackbar({
            message: t('community.featureInDevelopment'),
            status: ComponentStatus.WARNING,
          });
        }}
        title={t('community.reportPost')}
        titleStyle={{...TypoSkin.body3}}
      />
      {item.CustomerId === user?.Id && (
        <>
          <ListTile
            onPress={() => {
              hideBottomSheet(bottomSheetRef);
              navigation.push(RootScreen.createPost, {
                editPost: item,
                groupId: null,
              });
            }}
            title={t('community.editPost')}
            titleStyle={{...TypoSkin.body3}}
          />
          <ListTile
            onPress={() => {
              hideBottomSheet(bottomSheetRef);
              dispatch(newsFeedActions.deletePost(item));
            }}
            title={t('community.deletePost')}
            titleStyle={{...TypoSkin.body3}}
          />
        </>
      )}
    </View>
  );

  // Component hiển thị các nút hành động (Like, Comment, Share)
  const ActionButtons = () => (
    <View style={styles.actionContainer}>
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={styles.actionButton}
        onPress={handleLike}
        title={<Text style={styles.countText}>{item.Likes ?? 0}</Text>}
        textColor={
          item.IsLike === true
            ? ColorThemes.light.Error_Color_Main
            : ColorThemes.light.Neutral_Text_Color_Subtitle
        }
        prefixIconSize={12}
        prefixIcon={
          item.IsLike === true
            ? 'fill/emoticons/heart'
            : 'outline/emoticons/heart'
        }
      />
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={styles.actionButton}
        onPress={handleDetailPress}
        prefixIcon={'outline/user interface/b-comment'}
        prefixIconSize={12}
        textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
        title={<Text style={styles.countText}>{item.Comment ?? 0}</Text>}
      />
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        containerStyle={styles.actionButton}
        onPress={handleShare}
        prefixIcon={'fill/arrows/social-sharing'}
        prefixIconSize={12}
        textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      />
    </View>
  );

  // Component hiển thị các nút hành động bổ sung (Bookmark, More Options)
  const TrailingButtons = () => (
    <View style={styles.trailingContainer}>
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        onPress={handleBookmark}
        containerStyle={styles.iconButton}
        title={
          <Winicon
            src={
              item.IsBookmark === true
                ? 'fill/user interface/bookmark'
                : 'outline/user interface/bookmark'
            }
            size={14}
            color={
              item.IsBookmark === true
                ? ColorThemes.light.Warning_Color_Main
                : ColorThemes.light.Neutral_Text_Color_Subtitle
            }
          />
        }
      />
      <AppButton
        backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
        borderColor="transparent"
        onPress={handleMoreOptions}
        containerStyle={styles.iconButton}
        title={
          <Winicon
            src={'fill/user interface/menu-dots'}
            size={14}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
        }
      />
    </View>
  );

  // Chuẩn bị dữ liệu bài viết
  const postData = {
    ...item,
    relativeUser:
      item.CustomerId === user?.Id
        ? {
            image: user?.AvatarUrl,
            title: user?.Name,
            subtitle: Ultis.getDiffrentTime(item.DateCreated),
          }
        : item.relativeUser,
  };

  return (
    <DefaultPost
      data={postData}
      containerStyle={{paddingHorizontal: 0}}
      onPressDetail={handleDetailPress}
      actionView={<ActionButtons />}
      onPressHeader={handleHeaderPress}
      trailingView={<TrailingButtons />}
      showContent={true}
    />
  );
};

// Styles
const styles = StyleSheet.create({
  actionContainer: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    gap: 8,
  } as ViewStyle,

  actionButton: {
    padding: 4,
    height: 24,
    paddingVertical: 0,
    paddingHorizontal: 8,
  } as ViewStyle,

  iconButton: {
    borderRadius: 100,
    padding: 6,
    height: 24,
    width: 24,
  } as ViewStyle,

  trailingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  } as ViewStyle,

  bottomSheetContent: {
    gap: 8,
    height: Dimensions.get('window').height / 4,
    width: '100%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  } as ViewStyle,

  countText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  } as TextStyle,
});

export default PostItem;
