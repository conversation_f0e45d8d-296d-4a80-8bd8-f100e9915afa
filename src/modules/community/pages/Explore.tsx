import {DrawerActions, useNavigation, useRoute} from '@react-navigation/native';
import {useState, useRef, useEffect, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Dimensions,
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  Winicon,
  TextField,
  AppButton,
  showBottomSheet,
  ListTile,
  showSnackbar,
  ComponentStatus,
  HashTag,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {ProfileView} from './Chat';
import {DefaultPost, SkeletonPlacePostCard} from '../card/defaultPost';
import {Ultis} from '../../../utils/Utils';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {useNewsFeedData} from '../hook/newsFeedHook';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {onShare} from '../../../features/share';
import React from 'react';
import {LogoImg} from '../../../Screen/Page/Home';
import {useTag} from '../context/TagContext';
import {DataController} from '../../../base/baseController';
import {GroupDA} from '../groups/da';
import {CourseDA} from '../../Course/da';

// tạo danh sách tag
const tags = [
  {Id: 1, Name: 'Tất cả', key: 'all'},
  {Id: 2, Name: 'Bài viết', key: 'posts'},
  {Id: 3, Name: 'Khoá học', key: 'courses'},
  {Id: 4, Name: 'Nhóm', key: 'groups'},
];

export default function Explore() {
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const flatListRef = useRef<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const dispatch: AppDispatch = useDispatch();
  const router = useRoute<any>();
  const [searchValue, setSearchValue] = useState(router.params?.search ?? '');

  // Use context for tag management
  const {selectedTag} = useTag();
  const [selectedTagId, setSelectedTagId] = useState(0);

  // Data states for different content types
  const [allData, setAllData] = useState<any[]>([]);
  const [postsData, setPostsData] = useState<any[]>([]);
  const [groupsData, setGroupsData] = useState<any[]>([]);
  const [coursesData, setCoursesData] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [dataCounts, setDataCounts] = useState({
    all: 0,
    posts: 0,
    groups: 0,
    courses: 0,
  });

  // Handle selectedTag from context
  useEffect(() => {
    if (selectedTag) {
      console.log('Selected tag from context:', selectedTag);
      // Set search value to tag name or any property you want to search by
      // loại bỏ dấu #
      const tag =
        selectedTag.tag || selectedTag.name || selectedTag.title || '';
      const tagWithoutHash = tag.startsWith('#') ? tag.slice(1) : tag;
      setSearchValue(tagWithoutHash);
      setSelectedTagId(1); // Set to "Tất cả" when coming from tag context
    }
  }, [selectedTag]);

  // Reset selectedTagId when search value is cleared
  useEffect(() => {
    if (!searchValue) {
      setSelectedTagId(0);
      setAllData([]);
      setPostsData([]);
      setGroupsData([]);
      setCoursesData([]);
      setDataCounts({all: 0, posts: 0, groups: 0, courses: 0});
    }
  }, [searchValue]);

  // Debounce search value to prevent excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchValue(searchValue);
    }, 300); // 300ms delay
    return () => clearTimeout(timer);
  }, [searchValue]);

  // Fetch data when search value or selected tag changes
  useEffect(() => {
    if (debouncedSearchValue || selectedTagId > 0) {
      fetchDataByTag();
    }
  }, [debouncedSearchValue, selectedTagId]);

  // Data fetching functions
  const fetchPostsData = async (searchTerm: string = '') => {
    try {
      const postController = new DataController('Posts');
      let query = '-@IsHidden:{true}';

      if (searchTerm) {
        query = `@Name:(*${searchTerm}*) | @Content:(*${searchTerm}*) | @ListTag:(*${searchTerm}*) ${query}`;
      }

      const result = await postController.getListSimple({
        page: 1,
        size: 50,
        query,
        sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
      });

      if (result?.code === 200) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching posts:', error);
      return [];
    }
  };

  const fetchGroupsData = async (searchTerm: string = '') => {
    try {
      const groupDA = new GroupDA();
      let query = '*';

      if (searchTerm) {
        query = `@Name:(*${searchTerm}*) | @Description:(*${searchTerm}*)`;
      }

      const result = await groupDA.getList(1, 50, query);
      if (result?.code === 200) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching groups:', error);
      return [];
    }
  };

  const fetchCoursesData = async (searchTerm: string = '') => {
    try {
      const courseDA = new CourseDA();
      let query = '*';

      if (searchTerm) {
        query = `@Name:(*${searchTerm}*) | @Description:(*${searchTerm}*)`;
      }

      const result = await courseDA.getAllList(1, 50, query);
      if (result?.code === 200) {
        return result.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching courses:', error);
      return [];
    }
  };

  const fetchDataByTag = useCallback(
    async (tagId?: number) => {
      setSearchLoading(true);
      try {
        const searchTerm = debouncedSearchValue;
        const currentTagId = tagId !== undefined ? tagId : selectedTagId;

        // Fetch data based on selected tag
        let posts: any[] = [];
        let groups: any[] = [];
        let courses: any[] = [];

        if (currentTagId === 0 || currentTagId === 1) {
          // Tất cả
          [posts, groups, courses] = await Promise.all([
            fetchPostsData(searchTerm),
            fetchGroupsData(searchTerm),
            fetchCoursesData(searchTerm),
          ]);
        } else if (currentTagId === 2) {
          // Bài viết
          posts = await fetchPostsData(searchTerm);
        } else if (currentTagId === 3) {
          // Khoá học
          courses = await fetchCoursesData(searchTerm);
        } else if (currentTagId === 4) {
          // Nhóm
          groups = await fetchGroupsData(searchTerm);
        }

        // Add type identifier to each item
        const typedPosts = posts.map(item => ({...item, type: 'post'}));
        const typedGroups = groups.map(item => ({...item, type: 'group'}));
        const typedCourses = courses.map(item => ({...item, type: 'course'}));

        // Combine all data
        const combinedData = [...typedPosts, ...typedGroups, ...typedCourses];

        // Sort by DateCreated
        combinedData.sort(
          (a, b) => (b.DateCreated || 0) - (a.DateCreated || 0),
        );

        setAllData(combinedData);
        setPostsData(posts);
        setGroupsData(groups);
        setCoursesData(courses);

        // Update counts
        setDataCounts({
          all: combinedData.length,
          posts: posts.length,
          groups: groups.length,
          courses: courses.length,
        });
      } catch (error) {
        console.error('Error fetching data by tag:', error);
      } finally {
        setSearchLoading(false);
      }
    },
    [
      debouncedSearchValue,
      selectedTagId,
      fetchPostsData,
      fetchGroupsData,
      fetchCoursesData,
    ],
  );

  // Search function to filter posts (fallback for when no tag is selected)
  const searchPosts = useCallback((posts: any[], term: string) => {
    if (!term) return posts;

    const searchTerm = term.toLowerCase();
    return posts.filter((item: any) => {
      // Check post title/name
      const nameMatch = item?.Name?.toLowerCase().includes(searchTerm);
      // Check post content
      const contentMatch = item?.Content?.toLowerCase().includes(searchTerm);
      // Check author name if available
      const authorMatch = item?.relativeUser?.title
        ?.toLowerCase()
        .includes(searchTerm);
      // Check post tags if available
      const tagMatch = item?.tags?.some(
        (tag: any) =>
          tag?.name?.toLowerCase().includes(searchTerm) ||
          tag?.tag?.toLowerCase().includes(searchTerm),
      );

      return nameMatch || contentMatch || authorMatch || tagMatch;
    });
  }, []);

  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const size = 50;
  const {data, loading, page} = useNewsFeedData(1, size);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        await dispatch(newsFeedActions.getNewFeedPopular(1, size));
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };
  const handleLoadMore = async () => {
    // Kiểm tra các điều kiện để loadmore
    if (!loading && !isRefreshing && hasMore) {
      try {
        const result = newsFeedActions.getNewFeedPopular(page + 1, size);

        // Kiểm tra kết quả trả về
        if ((result as any)?.payload?.length < size) {
          setHasMore(false); // Nếu số lượng data nhỏ hơn size, đánh dấu là hết data
        } else if ((result as any)?.payload?.length === 0) {
          setHasMore(false); // Nếu không có data trả về, đánh dấu là hết data
        }
      } catch (error) {
        console.error('Load more error:', error);
        setHasMore(false); // Nếu có lỗi, đánh dấu là hết data
      }
    }
  };

  // Group Item Component
  const GroupItem = React.memo(({item}: {item: any}) => {
    return (
      <TouchableOpacity
        onPress={() => navigate(RootScreen.GroupIndex, {Id: item.Id})}
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          padding: 16,
          marginHorizontal: 16,
          borderRadius: 12,
          borderWidth: 1,
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
        }}>
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 12}}>
          <View
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
              backgroundColor: ColorThemes.light.Primary_Color_Background,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Winicon
              src="outline/user interface/users"
              size={24}
              color={ColorThemes.light.Primary_Color_Main}
            />
          </View>
          <View style={{flex: 1}}>
            <Text
              style={{
                ...TypoSkin.subtitle1,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}
              numberOfLines={1}>
              {item.Name}
            </Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                marginTop: 4,
              }}
              numberOfLines={2}>
              {item.Description || 'Nhóm cộng đồng'}
            </Text>
          </View>
          <Winicon
            src="outline/arrows/arrow-right"
            size={16}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
        </View>
      </TouchableOpacity>
    );
  });

  // Course Item Component
  const CourseItem = React.memo(({item}: {item: any}) => {
    return (
      <TouchableOpacity
        onPress={() => navigate(RootScreen.CourseDetail, {Id: item.Id})}
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          padding: 16,
          marginHorizontal: 16,
          borderRadius: 12,
          borderWidth: 1,
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
        }}>
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 12}}>
          <View
            style={{
              width: 48,
              height: 48,
              borderRadius: 8,
              backgroundColor: ColorThemes.light.Warning_Color_Background,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Winicon
              src="outline/education/book"
              size={24}
              color={ColorThemes.light.Warning_Color_Main}
            />
          </View>
          <View style={{flex: 1}}>
            <Text
              style={{
                ...TypoSkin.subtitle1,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}
              numberOfLines={1}>
              {item.Name}
            </Text>
            <Text
              style={{
                ...TypoSkin.body3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                marginTop: 4,
              }}
              numberOfLines={2}>
              {item.Description || 'Khóa học trực tuyến'}
            </Text>
            {item.Price && (
              <Text
                style={{
                  ...TypoSkin.buttonText5,
                  color: ColorThemes.light.Success_Color_Main,
                  marginTop: 4,
                }}>
                {item.Price === 0
                  ? 'Miễn phí'
                  : `${item.Price.toLocaleString()} VNĐ`}
              </Text>
            )}
          </View>
          <Winicon
            src="outline/arrows/arrow-right"
            size={16}
            color={ColorThemes.light.Neutral_Text_Color_Subtitle}
          />
        </View>
      </TouchableOpacity>
    );
  });

  const PostItem = React.memo(
    ({item, user, dialogRef}: {item: any; user: any; dialogRef: any}) => {
      return (
        <DefaultPost
          data={{
            ...item,
            relativeUser:
              item.CustomerId === user?.Id
                ? {
                    image: user?.AvatarUrl,
                    title: user?.Name,
                    subtitle: Ultis.getDiffrentTime(item.DateCreated),
                  }
                : item.relativeUser,
          }}
          containerStyle={{paddingHorizontal: 0}}
          onPressDetail={() => navigate(RootScreen.PostDetail, {item: item})}
          actionView={
            <View
              style={{
                flexDirection: 'row',
                paddingTop: 16,
                alignItems: 'center',
                gap: 8,
              }}>
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  if (user) {
                    await dispatch(
                      newsFeedActions.updateLike(item.Id, item.IsLike === true),
                    );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                    dialogCheckAcc(dialogRef);
                  }
                }}
                title={
                  <Text
                    style={{
                      ...TypoSkin.buttonText5,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {item.Likes ?? 0}
                  </Text>
                }
                textColor={
                  item.IsLike === true
                    ? ColorThemes.light.Error_Color_Main
                    : ColorThemes.light.Neutral_Text_Color_Subtitle
                }
                prefixIconSize={12}
                prefixIcon={
                  item.IsLike === true
                    ? 'fill/emoticons/heart'
                    : 'outline/emoticons/heart'
                }
              />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  navigate(RootScreen.PostDetail, {item: item});
                }}
                prefixIcon={'outline/user interface/b-comment'}
                prefixIconSize={12}
                textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                title={
                  <Text
                    style={{
                      ...TypoSkin.buttonText5,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {item.Comment ?? 0}
                  </Text>
                }
              />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  onShare({content: 'Hello world'});
                }}
                prefixIcon={'fill/arrows/social-sharing'}
                prefixIconSize={12}
                textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
            </View>
          }
          onPressHeader={() => {
            if (item.GroupId) {
              navigate(RootScreen.GroupIndex, {Id: item.GroupId});
            } else {
              navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
            }
          }}
          trailingView={
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                onPress={async () => {
                  if (user) {
                    await dispatch(
                      newsFeedActions.addBookmark(
                        item.Id,
                        item.IsBookmark === true,
                      ),
                    );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                    dialogCheckAcc(dialogRef);
                  }
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 24,
                  width: 24,
                }}
                title={
                  <Winicon
                    src={
                      item.IsBookmark === true
                        ? 'fill/user interface/bookmark'
                        : 'outline/user interface/bookmark'
                    }
                    size={14}
                    color={
                      item.IsBookmark === true
                        ? ColorThemes.light.Warning_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle
                    }
                  />
                }
              />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                onPress={() => {
                  showBottomSheet({
                    ref: bottomSheetRef,
                    title: t('community.actions'),
                    suffixAction: <View />,
                    prefixAction: (
                      <TouchableOpacity
                        onPress={() => hideBottomSheet(bottomSheetRef)}
                        style={{padding: 6, alignItems: 'center'}}>
                        <Winicon
                          src="outline/layout/xmark"
                          size={20}
                          color={ColorThemes.light.Neutral_Text_Color_Body}
                        />
                      </TouchableOpacity>
                    ),
                    children: (
                      <View
                        style={{
                          gap: 8,
                          height: Dimensions.get('window').height / 4,
                          width: '100%',
                          backgroundColor:
                            ColorThemes.light.Neutral_Background_Color_Absolute,
                        }}>
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            showSnackbar({
                              message: t('community.featureInDevelopment'),
                              status: ComponentStatus.WARNING,
                            });
                          }}
                          title={t('community.reportPost')}
                          titleStyle={{...TypoSkin.body3}}
                        />
                        {item.CustomerId === user.Id && (
                          <ListTile
                            onPress={() => {
                              hideBottomSheet(bottomSheetRef);

                              navigation.push(RootScreen.createPost, {
                                editPost: item,
                                groupId: null,
                              });
                              // showSnackbar({
                              //   message: 'Chức năng đang được phát triển',
                              //   status: ComponentStatus.WARNING,
                              // });
                            }}
                            title={t('community.editPost')}
                            titleStyle={{...TypoSkin.body3}}
                          />
                        )}
                        {item.CustomerId === user.Id && (
                          <ListTile
                            onPress={() => {
                              hideBottomSheet(bottomSheetRef);
                              dispatch(newsFeedActions.deletePost(item));
                            }}
                            title={t('community.deletePost')}
                            titleStyle={{...TypoSkin.body3}}
                          />
                        )}
                      </View>
                    ),
                  });
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 24,
                  width: 24,
                }}
                title={
                  <Winicon
                    src={'fill/user interface/menu-dots'}
                    size={14}
                    color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  />
                }
              />
            </View>
          }
          showContent={true}
        />
      );
    },
  );

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <LogoImg />
          </TouchableOpacity>
        }
        title={t('community.tabs.explore')}
        trailing={<ProfileView />}
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              height: 56,
              gap: 8,
              paddingTop: 16,
              paddingBottom: 16,
            }}>
            <TextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={(vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder={t('common.search')}
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
              suffix={
                searchValue ? (
                  <TouchableOpacity
                    onPress={() => setSearchValue('')}
                    style={{padding: 4}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={14}
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                    />
                  </TouchableOpacity>
                ) : null
              }
            />
          </View>
        }
      />
      {/* total tìm kiếm */}
      {searchValue ? (
        <View style={{paddingHorizontal: 16}}>
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              flexWrap: 'wrap',
              paddingVertical: 8,
            }}>
            {tags.map((item, index) => {
              const count =
                dataCounts[item.key as keyof typeof dataCounts] || 0;
              return (
                <HashTag
                  key={index}
                  title={`${item.Name} (${count})`}
                  onPress={async () => {
                    setSelectedTagId(item.Id);
                    // Trigger data fetch immediately when tag is selected
                    if (debouncedSearchValue) {
                      await fetchDataByTag(item.Id);
                    }
                  }}
                  styles={{
                    borderRadius: 24,
                    backgroundColor:
                      selectedTagId === item.Id
                        ? ColorThemes.light.Primary_Color_Background
                        : ColorThemes.light.Neutral_Background_Color_Main,
                    paddingHorizontal: 8,
                    paddingVertical: 4,
                    margin: 0,
                    alignItems: 'flex-start',
                    height: undefined,
                  }}
                  textStyles={{
                    ...TypoSkin.buttonText6,
                    color:
                      selectedTagId === item.Id
                        ? ColorThemes.light.Primary_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}
                />
              );
            })}
          </View>
        </View>
      ) : null}

      <FlatList
        ref={flatListRef}
        data={
          searchValue && selectedTagId > 0
            ? allData
            : searchPosts(data, debouncedSearchValue)
        }
        renderItem={({item}) => {
          if (searchValue && selectedTagId > 0) {
            // Render different types based on item type
            if (item.type === 'group') {
              return <GroupItem item={item} />;
            } else if (item.type === 'course') {
              return <CourseItem item={item} />;
            } else {
              return <PostItem item={item} user={user} dialogRef={dialogRef} />;
            }
          } else {
            return <PostItem item={item} user={user} dialogRef={dialogRef} />;
          }
        }}
        keyExtractor={item => `${item.type || 'post'}-${item.Id.toString()}`}
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={{
          gap: 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading || searchLoading) {
            return (
              <View style={{gap: 8}}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlacePostCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          }
          return <EmptyPage />;
        }}
        ListFooterComponent={() => {
          if ((loading || searchLoading) && !isRefreshing) {
            return <SkeletonPlacePostCard />;
          }
          if (!hasMore && data.length > 0 && !searchValue) {
            return (
              <View
                style={{
                  padding: 16,
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    ...TypoSkin.subtitle2,
                  }}>
                  {t('common.noMoreData')}
                </Text>
              </View>
            );
          }
          return null;
        }}
      />
    </View>
  );
}
