import {BackgroundData} from '../../../../redux/models/PostBackground';

export type TextSegment = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
};

export interface TextFormat {
  bold: boolean;
  italic: boolean;
  underline: boolean;
}

export type ImageItem = {
  id: string;
  uri: string;
  path: string;
  mime: string;
  filename?: string;
  width?: number;
  height?: number;
  existingId?: string;
};

export type PostData = {
  text: string;
  segments: TextSegment[];
  images: ImageItem[];
  html?: string;
  existingImageIds?: string[]; // Thêm trường này
  backgroundData?: BackgroundData | null;
};

export type RichTextComposerProps = {
  onTextChange?: (text: string) => void;
  onImagesChange?: (images: ImageItem[]) => void;
  onDataChange?: (data: PostData) => void;
  initialText?: string;
  initialImages?: ImageItem[];
  initialHtml?: string;
  maxImages?: number;
  // Thêm prop để nhận danh sách ID ảnh từ bài post cần edit
  initialImageIds?: string[];
};

export type RichTextComposerRef = {
  getPostData: () => PostData;
  clearContent: () => void;
  setContent: (
    text: string,
    images?: ImageItem[],
    html?: string,
    imageIds?: string[],
  ) => void;
  focus: () => void;
};

export type FormatIconProps = {
  label: string;
  active: boolean;
  onPress: () => void;
};
