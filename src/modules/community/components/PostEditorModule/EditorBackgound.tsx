// PostInputComponent.js

import React from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  SafeAreaView,
  StatusBar,
  ImageBackground,
  ViewStyle,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {BackgroundData} from '../../../../redux/models/PostBackground';

interface EditorBackgroundProps {
  background?: BackgroundData;
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  style?: ViewStyle;
}

/**
 * @description Component nhập liệu cho bài post với background tùy chỉnh theo BackgroundData.
 * @param {object} props
 * @param {BackgroundData} [props.background] - Dữ liệu background (solid, gradient, image, empty)
 * @param {string} [props.value] - G<PERSON><PERSON> trị text hiện tại
 * @param {function} [props.onChangeText] - Callback khi text thay đổi
 * @param {string} [props.placeholder] - Text placeholder
 * @param {ViewStyle} [props.style] - Custom style cho container
 */
const EditorBackground: React.FC<EditorBackgroundProps> = ({
  background,
  value,
  onChangeText,
  placeholder = 'Bạn đang nghĩ gì?',
  style,
}) => {
  // Xác định màu text - sử dụng TextColor nếu có, không thì dùng white mặc định
  const textColor = background?.TextColor || 'white';
  const placeholderColor = background?.TextColor
    ? `${background.TextColor}80`
    : '#E0E0E0'; // Add transparency

  const renderContent = () => (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="light-content" />
      <TextInput
        style={[styles.textInput, {color: textColor}]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={placeholderColor}
        multiline={true}
        textAlignVertical="center"
      />
    </SafeAreaView>
  );

  // Nếu không có background hoặc Type là 999 (empty), trả về view mặc định
  if (!background || background.Type === 999) {
    return (
      <View style={[styles.container, styles.defaultBackground, style]}>
        {renderContent()}
      </View>
    );
  }

  switch (background.Type) {
    case 1: // Solid color
      return (
        <View
          style={[
            styles.container,
            {backgroundColor: background.Color},
            style,
          ]}>
          {renderContent()}
        </View>
      );

    case 2: // Gradient
      // Parse gradient colors từ string "color1, color2" thành array
      const gradientColors = background.Color
        ? background.Color.split(',').map(color => color.trim())
        : ['#6A0DAD', '#4c669f'];
      return (
        <LinearGradient
          colors={gradientColors}
          style={[styles.container, style]}>
          {renderContent()}
        </LinearGradient>
      );

    case 3: // Image background
      return (
        <ImageBackground
          source={{uri: background.Img}}
          style={[styles.container, style]}
          resizeMode="cover">
          <View style={styles.imageOverlay}>{renderContent()}</View>
        </ImageBackground>
      );

    default:
      // Fallback - trả về view mặc định
      return (
        <View style={[styles.container, styles.defaultBackground, style]}>
          {renderContent()}
        </View>
      );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultBackground: {
    backgroundColor: '#6A0DAD', // Màu mặc định khi không có background
  },
  safeArea: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInput: {
    width: '90%', // Chiều rộng 90% để không quá sát 2 bên
    fontSize: 28,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  imageOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Overlay tối nhẹ để text dễ đọc trên background image
    width: '100%',
  },
});

export default EditorBackground;
