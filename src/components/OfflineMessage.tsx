import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../assets/skin/colors';
import {TypoSkin} from '../assets/skin/typography';
import {useTranslation} from 'react-i18next';
import EmptyPage from '../Screen/emptyPage';

interface OfflineMessageProps {
  show: boolean;
}

const OfflineMessage: React.FC<OfflineMessageProps> = ({show}) => {
  const {t} = useTranslation();

  if (!show) return null;

  return <EmptyPage title={t('app.offline', 'Bạn đang offline')} />;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.Warning_Color_Background,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Warning_Color_Border,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  text: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Warning_Color_Main,
    fontWeight: '500',
  },
});

export default OfflineMessage;
