import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { SavedVideo } from './VideoDownloadManager';
import { getObjToAsyncStorage, saveObjToAsyncStorage } from './AsyncStorage';

export interface VideoStorageInfo {
  totalVideos: number;
  totalSize: number;
  totalSizeFormatted: string;
  availableSpace: number;
  availableSpaceFormatted: string;
}

export interface VideoGroupedByLesson {
  lessonId: string;
  lessonName: string;
  courseId: string;
  videos: SavedVideo[];
  totalSize: number;
  totalSizeFormatted: string;
}

class OfflineVideoStorage {
  private static instance: OfflineVideoStorage;
  private readonly STORAGE_KEY = 'SAVED_VIDEOS';
  private readonly DOWNLOAD_DIR = Platform.OS === 'ios' 
    ? RNFS.DocumentDirectoryPath + '/SavedVideos'
    : RNFS.DownloadDirectoryPath + '/SavedVideos';

  private constructor() {}

  public static getInstance(): OfflineVideoStorage {
    if (!OfflineVideoStorage.instance) {
      OfflineVideoStorage.instance = new OfflineVideoStorage();
    }
    return OfflineVideoStorage.instance;
  }

  /**
   * Format bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get all saved videos from storage
   */
  public async getAllSavedVideos(): Promise<SavedVideo[]> {
    try {
      const savedVideos = await getObjToAsyncStorage(this.STORAGE_KEY);
      return savedVideos || [];
    } catch (error) {
      console.error('Error getting saved videos:', error);
      return [];
    }
  }

  /**
   * Get only completed videos
   */
  public async getCompletedVideos(): Promise<SavedVideo[]> {
    try {
      const allVideos = await this.getAllSavedVideos();
      const completedVideos = allVideos.filter(video => video.downloadStatus === 'completed');
      
      // Verify files still exist
      const verifiedVideos: SavedVideo[] = [];
      for (const video of completedVideos) {
        try {
          const exists = await RNFS.exists(video.localPath);
          if (exists) {
            verifiedVideos.push(video);
          } else {
            console.warn(`Video file not found: ${video.localPath}`);
            // Remove from storage if file doesn't exist
            await this.removeVideoFromStorage(video.id);
          }
        } catch (error) {
          console.warn(`Error checking video file: ${video.localPath}`, error);
        }
      }
      
      return verifiedVideos;
    } catch (error) {
      console.error('Error getting completed videos:', error);
      return [];
    }
  }

  /**
   * Get videos grouped by lesson
   */
  public async getVideosGroupedByLesson(): Promise<VideoGroupedByLesson[]> {
    try {
      const completedVideos = await this.getCompletedVideos();
      const grouped: { [key: string]: VideoGroupedByLesson } = {};
      
      for (const video of completedVideos) {
        if (!grouped[video.lessonId]) {
          grouped[video.lessonId] = {
            lessonId: video.lessonId,
            lessonName: video.lessonName,
            courseId: video.courseId,
            videos: [],
            totalSize: 0,
            totalSizeFormatted: '0 B',
          };
        }
        
        grouped[video.lessonId].videos.push(video);
        
        // Calculate size
        try {
          const stat = await RNFS.stat(video.localPath);
          grouped[video.lessonId].totalSize += stat.size;
        } catch (error) {
          console.warn(`Could not get size for ${video.localPath}:`, error);
        }
      }
      
      // Format sizes and sort
      const result = Object.values(grouped).map(group => ({
        ...group,
        totalSizeFormatted: this.formatBytes(group.totalSize),
        videos: group.videos.sort((a, b) => a.videoName.localeCompare(b.videoName)),
      }));
      
      return result.sort((a, b) => a.lessonName.localeCompare(b.lessonName));
    } catch (error) {
      console.error('Error grouping videos by lesson:', error);
      return [];
    }
  }

  /**
   * Get storage information
   */
  public async getStorageInfo(): Promise<VideoStorageInfo> {
    try {
      const completedVideos = await this.getCompletedVideos();
      let totalSize = 0;
      
      // Calculate total size of downloaded videos
      for (const video of completedVideos) {
        try {
          const stat = await RNFS.stat(video.localPath);
          totalSize += stat.size;
        } catch (error) {
          console.warn(`Could not get size for ${video.localPath}:`, error);
        }
      }
      
      // Get available space
      let availableSpace = 0;
      try {
        const fsInfo = await RNFS.getFSInfo();
        availableSpace = fsInfo.freeSpace;
      } catch (error) {
        console.warn('Could not get filesystem info:', error);
      }
      
      return {
        totalVideos: completedVideos.length,
        totalSize,
        totalSizeFormatted: this.formatBytes(totalSize),
        availableSpace,
        availableSpaceFormatted: this.formatBytes(availableSpace),
      };
    } catch (error) {
      console.error('Error getting storage info:', error);
      return {
        totalVideos: 0,
        totalSize: 0,
        totalSizeFormatted: '0 B',
        availableSpace: 0,
        availableSpaceFormatted: '0 B',
      };
    }
  }

  /**
   * Get video by ID
   */
  public async getVideoById(videoId: string): Promise<SavedVideo | null> {
    try {
      const allVideos = await this.getAllSavedVideos();
      const video = allVideos.find(v => v.id === videoId);
      
      if (video && video.downloadStatus === 'completed') {
        // Verify file exists
        const exists = await RNFS.exists(video.localPath);
        if (exists) {
          return video;
        } else {
          // Remove from storage if file doesn't exist
          await this.removeVideoFromStorage(videoId);
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error getting video by ID:', error);
      return null;
    }
  }

  /**
   * Check if video exists locally
   */
  public async isVideoAvailableOffline(lessonId: string, videoUrl: string): Promise<SavedVideo | null> {
    try {
      const allVideos = await this.getAllSavedVideos();
      const video = allVideos.find(v => 
        v.lessonId === lessonId && 
        v.videoUrl === videoUrl && 
        v.downloadStatus === 'completed'
      );
      
      if (video) {
        // Verify file exists
        const exists = await RNFS.exists(video.localPath);
        if (exists) {
          return video;
        } else {
          // Remove from storage if file doesn't exist
          await this.removeVideoFromStorage(video.id);
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error checking if video is available offline:', error);
      return null;
    }
  }

  /**
   * Remove video from storage (metadata only)
   */
  private async removeVideoFromStorage(videoId: string): Promise<void> {
    try {
      const allVideos = await this.getAllSavedVideos();
      const updatedVideos = allVideos.filter(v => v.id !== videoId);
      await saveObjToAsyncStorage(this.STORAGE_KEY, updatedVideos);
    } catch (error) {
      console.error('Error removing video from storage:', error);
    }
  }

  /**
   * Delete video file and remove from storage
   */
  public async deleteVideo(videoId: string): Promise<boolean> {
    try {
      const video = await this.getVideoById(videoId);
      if (!video) {
        return false;
      }
      
      // Delete file
      const exists = await RNFS.exists(video.localPath);
      if (exists) {
        await RNFS.unlink(video.localPath);
      }
      
      // Remove from storage
      await this.removeVideoFromStorage(videoId);
      
      return true;
    } catch (error) {
      console.error('Error deleting video:', error);
      return false;
    }
  }

  /**
   * Delete all videos for a lesson
   */
  public async deleteVideosByLesson(lessonId: string): Promise<boolean> {
    try {
      const allVideos = await this.getAllSavedVideos();
      const lessonVideos = allVideos.filter(v => v.lessonId === lessonId);
      
      for (const video of lessonVideos) {
        try {
          const exists = await RNFS.exists(video.localPath);
          if (exists) {
            await RNFS.unlink(video.localPath);
          }
        } catch (error) {
          console.warn(`Error deleting video file: ${video.localPath}`, error);
        }
      }
      
      // Remove from storage
      const updatedVideos = allVideos.filter(v => v.lessonId !== lessonId);
      await saveObjToAsyncStorage(this.STORAGE_KEY, updatedVideos);
      
      return true;
    } catch (error) {
      console.error('Error deleting videos by lesson:', error);
      return false;
    }
  }

  /**
   * Clear all downloaded videos
   */
  public async clearAllVideos(): Promise<boolean> {
    try {
      const allVideos = await this.getAllSavedVideos();
      
      // Delete all files
      for (const video of allVideos) {
        try {
          const exists = await RNFS.exists(video.localPath);
          if (exists) {
            await RNFS.unlink(video.localPath);
          }
        } catch (error) {
          console.warn(`Error deleting video file: ${video.localPath}`, error);
        }
      }
      
      // Clear storage
      await saveObjToAsyncStorage(this.STORAGE_KEY, []);

      // Clean up download directory but don't remove it
      try {
        const dirExists = await RNFS.exists(this.DOWNLOAD_DIR);
        if (dirExists) {
          // Read all files in directory and delete them
          const files = await RNFS.readDir(this.DOWNLOAD_DIR);
          for (const file of files) {
            try {
              await RNFS.unlink(file.path);
            } catch (error) {
              console.warn(`Error deleting file: ${file.path}`, error);
            }
          }
        }
      } catch (error) {
        console.warn('Error cleaning download directory:', error);
      }
      
      return true;
    } catch (error) {
      console.error('Error clearing all videos:', error);
      return false;
    }
  }

  /**
   * Get videos that are currently downloading
   */
  public async getDownloadingVideos(): Promise<SavedVideo[]> {
    try {
      const allVideos = await this.getAllSavedVideos();
      return allVideos.filter(v => 
        v.downloadStatus === 'downloading' || 
        v.downloadStatus === 'paused' || 
        v.downloadStatus === 'pending'
      );
    } catch (error) {
      console.error('Error getting downloading videos:', error);
      return [];
    }
  }

  /**
   * Clean up orphaned files (files without metadata)
   */
  public async cleanupOrphanedFiles(): Promise<void> {
    try {
      const dirExists = await RNFS.exists(this.DOWNLOAD_DIR);
      if (!dirExists) {
        return;
      }
      
      const files = await RNFS.readDir(this.DOWNLOAD_DIR);
      const allVideos = await this.getAllSavedVideos();
      const validPaths = new Set(allVideos.map(v => v.localPath));
      
      for (const file of files) {
        if (!validPaths.has(file.path)) {
          try {
            await RNFS.unlink(file.path);
            console.log(`Cleaned up orphaned file: ${file.path}`);
          } catch (error) {
            console.warn(`Error deleting orphaned file: ${file.path}`, error);
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up orphaned files:', error);
    }
  }
}

export default OfflineVideoStorage;
