import {DataController} from '../../base/baseController';
import {BackgroundData} from '../models/PostBackground';
import {getImage} from './rootAction';

export const postBackgroundAction = {
  fetchAll: async (): Promise<BackgroundData[]> => {
    const controller = new DataController('PostBackgroundM');
    const response = await controller.getAll();

    if (response.code === 200) {
      let data = response.data;
      data = await getImage({items: data});
      return data;
    }
    return [];
  },
};
