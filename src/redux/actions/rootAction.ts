// đ<PERSON><PERSON> là nơi chứa các action cần thiết cho các module

import ConfigAPI from '../../Config/ConfigAPI';

const getImage = async ({items}: {items: any}) => {
  if (!items || items.length <= 0) return [];
  const cloneItems = [...items];
  const url = ConfigAPI.urlImg;

  for (const item of cloneItems) {
    if (item.Img) {
      item.Img = `${url}${item.Img}`;
    }
  }

  return cloneItems;
};

export {getImage};
