import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../store/store';
import {useEffect, useRef} from 'react';
import {LessonActions} from '../reducers/proccessLessonReducer';



// Hook mới cho Part-based roadmap
export function useProccessPartLesson(id?: string) {
  const dispatch: AppDispatch = useDispatch();
  const lessons = useSelector((state: RootState) => state.process.lessons);
  const loading = useSelector((state: RootState) => state.process.loading);
  const error = useSelector((state: RootState) => state.process.error);

  const fetchedIdsRef = useRef<Set<string>>(new Set());

  useEffect(() => {
    if (id && !fetchedIdsRef.current.has(id)) {
      fetchedIdsRef.current.add(id);
      dispatch(LessonActions.getProccessPartLesson(id));
    }
  }, [dispatch, id]);

  return {lessons, loading, error};
}
