import {createSlice, createAsyncThunk} from '@reduxjs/toolkit';
import {postBackgroundAction} from '../actions/PostBackgroundAction';
import {BackgroundData} from '../models/PostBackground';

export const fetchPostBackgrounds = createAsyncThunk(
  'postBackground/fetchPostBackgrounds',
  async (_, {rejectWithValue}) => {
    try {
      const data = await postBackgroundAction.fetchAll();
      return data;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  },
);

interface PostBackgroundState {
  items: BackgroundData[];
  loading: boolean;
  error: string | null | unknown;
}

const initialState: PostBackgroundState = {
  items: [],
  loading: false,
  error: null,
};

export const postBackgroundSlice = createSlice({
  name: 'postBackground',
  initialState,
  reducers: {
    setData(state, action) {
      const {stateName, value} = action.payload;
      (state as any)[stateName] = value;
    },
  },
  extraReducers: builder => {
    builder
      .addCase(fetchPostBackgrounds.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPostBackgrounds.fulfilled, (state, action) => {
        state.loading = false;
        action.payload.unshift({
          Id: '999',
          Type: 999,
          Img: '',
          Color: '',
          TextColor: '',
        });
        state.items = action.payload;
        console.log(state.items);
      })
      .addCase(fetchPostBackgrounds.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export default postBackgroundSlice.reducer;
